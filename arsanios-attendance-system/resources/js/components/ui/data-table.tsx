import * as React from 'react';
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
    Table as TanStackTable,
} from '@tanstack/react-table';
import { ArrowUpDown, ChevronDown, Search, Filter, X, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    searchPlaceholder?: string;
    onSearch?: (query: string) => void;
    searchQuery?: string;
    isSearching?: boolean;
    searchSuggestions?: string[];
    onSuggestionSelect?: (suggestion: string) => void;
    filters?: React.ReactNode;
    toolbar?: React.ReactNode;
    className?: string;
    enableColumnVisibility?: boolean;
    enableGlobalFilter?: boolean;
    enableSorting?: boolean;
    enablePagination?: boolean;
    pageSize?: number;
    isRTL?: boolean;
}

export function DataTable<TData, TValue>({
    columns,
    data,
    searchPlaceholder = "البحث...",
    onSearch,
    searchQuery = "",
    isSearching = false,
    searchSuggestions = [],
    onSuggestionSelect,
    filters,
    toolbar,
    className,
    enableColumnVisibility = true,
    enableGlobalFilter = true,
    enableSorting = true,
    enablePagination = true,
    pageSize = 10,
    isRTL = true,
}: DataTableProps<TData, TValue>) {
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const [globalFilter, setGlobalFilter] = React.useState(searchQuery);
    const [showSuggestions, setShowSuggestions] = React.useState(false);
    const searchInputRef = React.useRef<HTMLInputElement>(null);

    const table = useReactTable({
        data,
        columns,
        onSortingChange: enableSorting ? setSorting : undefined,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
        getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: 'includesString',
        state: {
            sorting: enableSorting ? sorting : [],
            columnFilters,
            columnVisibility,
            rowSelection,
            globalFilter: enableGlobalFilter ? globalFilter : undefined,
        },
        initialState: {
            pagination: {
                pageSize,
            },
        },
    });

    // Handle search input changes
    const handleSearchChange = (value: string) => {
        setGlobalFilter(value);
        if (onSearch) {
            onSearch(value);
        }
        setShowSuggestions(value.length > 1 && searchSuggestions.length > 0);
    };

    // Handle suggestion selection
    const handleSuggestionSelect = (suggestion: string) => {
        setGlobalFilter(suggestion);
        if (onSuggestionSelect) {
            onSuggestionSelect(suggestion);
        }
        setShowSuggestions(false);
        searchInputRef.current?.focus();
    };

    // Clear search
    const clearSearch = () => {
        setGlobalFilter("");
        if (onSearch) {
            onSearch("");
        }
        setShowSuggestions(false);
    };

    React.useEffect(() => {
        setGlobalFilter(searchQuery);
    }, [searchQuery]);

    return (
        <div className={cn("w-full space-y-4", className)} dir={isRTL ? "rtl" : "ltr"}>
            {/* Search and Filters */}
            <div className="flex items-center justify-between">
                <div className="flex flex-1 items-center space-x-2 space-x-reverse">
                    {enableGlobalFilter && (
                        <div className="relative flex-1 max-w-sm">
                            <div className="relative">
                                <Search className={cn(
                                    "absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground",
                                    isRTL ? "right-3" : "left-3"
                                )} />
                                <Input
                                    ref={searchInputRef}
                                    placeholder={searchPlaceholder}
                                    value={globalFilter ?? ""}
                                    onChange={(event) => handleSearchChange(event.target.value)}
                                    className={cn(
                                        "h-10",
                                        isRTL ? "pr-10 pl-10" : "pl-10 pr-10"
                                    )}
                                />
                                {isSearching && (
                                    <Loader2 className={cn(
                                        "absolute top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground",
                                        isRTL ? "left-3" : "right-3"
                                    )} />
                                )}
                                {globalFilter && !isSearching && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className={cn(
                                            "absolute top-1/2 h-4 w-4 -translate-y-1/2 p-0 hover:bg-transparent",
                                            isRTL ? "left-3" : "right-3"
                                        )}
                                        onClick={clearSearch}
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                )}
                            </div>
                            
                            {/* Search Suggestions */}
                            {showSuggestions && searchSuggestions.length > 0 && (
                                <div className="absolute top-full z-50 mt-1 w-full rounded-md border bg-popover p-1 shadow-md">
                                    {searchSuggestions.map((suggestion, index) => (
                                        <button
                                            key={index}
                                            className="w-full rounded-sm px-2 py-1.5 text-sm text-right hover:bg-accent hover:text-accent-foreground"
                                            onClick={() => handleSuggestionSelect(suggestion)}
                                        >
                                            {suggestion}
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                    
                    {filters}
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                    {toolbar}
                    
                    {enableColumnVisibility && (
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" className="ml-auto">
                                    <Filter className="mr-2 h-4 w-4" />
                                    الأعمدة
                                    <ChevronDown className="ml-2 h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                                {table
                                    .getAllColumns()
                                    .filter((column) => column.getCanHide())
                                    .map((column) => {
                                        return (
                                            <DropdownMenuCheckboxItem
                                                key={column.id}
                                                className="capitalize"
                                                checked={column.getIsVisible()}
                                                onCheckedChange={(value) =>
                                                    column.toggleVisibility(!!value)
                                                }
                                            >
                                                {column.id}
                                            </DropdownMenuCheckboxItem>
                                        );
                                    })}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    )}
                </div>
            </div>

            {/* Active Filters */}
            {columnFilters.length > 0 && (
                <div className="flex flex-wrap gap-2">
                    {columnFilters.map((filter) => (
                        <Badge key={filter.id} variant="secondary" className="gap-1">
                            {filter.id}: {String(filter.value)}
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-auto p-0 text-muted-foreground hover:text-foreground"
                                onClick={() => {
                                    setColumnFilters(prev => 
                                        prev.filter(f => f.id !== filter.id)
                                    );
                                }}
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        </Badge>
                    ))}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setColumnFilters([])}
                        className="h-auto px-2 py-1"
                    >
                        مسح الكل
                    </Button>
                </div>
            )}

            {/* Table */}
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} className="text-right">
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef.header,
                                                  header.getContext()
                                              )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                    className="hover:bg-muted/50"
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id} className="text-right">
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    لا توجد نتائج.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            {enablePagination && (
                <div className="flex items-center justify-between px-2">
                    <div className="flex-1 text-sm text-muted-foreground">
                        {table.getFilteredSelectedRowModel().rows.length} من{" "}
                        {table.getFilteredRowModel().rows.length} صف محدد.
                    </div>
                    <div className="flex items-center space-x-6 space-x-reverse lg:space-x-8">
                        <div className="flex items-center space-x-2 space-x-reverse">
                            <p className="text-sm font-medium">صفوف لكل صفحة</p>
                            <select
                                value={table.getState().pagination.pageSize}
                                onChange={(e) => {
                                    table.setPageSize(Number(e.target.value));
                                }}
                                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
                            >
                                {[10, 20, 30, 40, 50].map((pageSize) => (
                                    <option key={pageSize} value={pageSize}>
                                        {pageSize}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            صفحة {table.getState().pagination.pageIndex + 1} من{" "}
                            {table.getPageCount()}
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={() => table.setPageIndex(0)}
                                disabled={!table.getCanPreviousPage()}
                            >
                                <span className="sr-only">الذهاب إلى الصفحة الأولى</span>
                                {"<<"}
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={() => table.previousPage()}
                                disabled={!table.getCanPreviousPage()}
                            >
                                <span className="sr-only">الذهاب إلى الصفحة السابقة</span>
                                {"<"}
                            </Button>
                            <Button
                                variant="outline"
                                className="h-8 w-8 p-0"
                                onClick={() => table.nextPage()}
                                disabled={!table.getCanNextPage()}
                            >
                                <span className="sr-only">الذهاب إلى الصفحة التالية</span>
                                {">"}
                            </Button>
                            <Button
                                variant="outline"
                                className="hidden h-8 w-8 p-0 lg:flex"
                                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                                disabled={!table.getCanNextPage()}
                            >
                                <span className="sr-only">الذهاب إلى الصفحة الأخيرة</span>
                                {">>"}
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

// Sortable column header component
export function SortableHeader({ column, children, className }: {
    column: any;
    children: React.ReactNode;
    className?: string;
}) {
    return (
        <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className={cn("h-auto p-0 hover:bg-transparent", className)}
        >
            {children}
            <ArrowUpDown className="mr-2 h-4 w-4" />
        </Button>
    );
}
