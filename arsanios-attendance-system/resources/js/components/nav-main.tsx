import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { useState } from 'react';

// Modern color palette for navigation items
const navigationColors = [
    {
        gradient: 'from-blue-500 to-indigo-600',
        hoverGradient: 'from-blue-600 to-indigo-700',
        iconBg: 'bg-blue-100 dark:bg-blue-900/30',
        iconColor: 'text-blue-600 dark:text-blue-400',
        shadow: 'shadow-blue-500/25',
    },
    {
        gradient: 'from-emerald-500 to-teal-600',
        hoverGradient: 'from-emerald-600 to-teal-700',
        iconBg: 'bg-emerald-100 dark:bg-emerald-900/30',
        iconColor: 'text-emerald-600 dark:text-emerald-400',
        shadow: 'shadow-emerald-500/25',
    },
    {
        gradient: 'from-purple-500 to-pink-600',
        hoverGradient: 'from-purple-600 to-pink-700',
        iconBg: 'bg-purple-100 dark:bg-purple-900/30',
        iconColor: 'text-purple-600 dark:text-purple-400',
        shadow: 'shadow-purple-500/25',
    },
    {
        gradient: 'from-orange-500 to-red-600',
        hoverGradient: 'from-orange-600 to-red-700',
        iconBg: 'bg-orange-100 dark:bg-orange-900/30',
        iconColor: 'text-orange-600 dark:text-orange-400',
        shadow: 'shadow-orange-500/25',
    },
    {
        gradient: 'from-cyan-500 to-blue-600',
        hoverGradient: 'from-cyan-600 to-blue-700',
        iconBg: 'bg-cyan-100 dark:bg-cyan-900/30',
        iconColor: 'text-cyan-600 dark:text-cyan-400',
        shadow: 'shadow-cyan-500/25',
    },
    {
        gradient: 'from-rose-500 to-pink-600',
        hoverGradient: 'from-rose-600 to-pink-700',
        iconBg: 'bg-rose-100 dark:bg-rose-900/30',
        iconColor: 'text-rose-600 dark:text-rose-400',
        shadow: 'shadow-rose-500/25',
    },
];

export function NavMain({ items = [] }: { items: NavItem[] }) {
    const page = usePage();
    const [hoveredItem, setHoveredItem] = useState<string | null>(null);

    return (
        <SidebarGroup className="font-cairo px-3 py-2">
            <SidebarGroupLabel className="mb-4 text-sm font-semibold tracking-wide text-sidebar-foreground/80">صفحات النظام</SidebarGroupLabel>
            <SidebarMenu className="space-y-2">
                {items.map((item, index) => {
                    const isActive = page.url.startsWith(item.href);
                    const colorScheme = navigationColors[index % navigationColors.length];

                    return (
                        <SidebarMenuItem key={item.title} className="group">
                            <SidebarMenuButton
                                asChild
                                isActive={isActive}
                                tooltip={{ children: item.title }}
                                className={cn(
                                    'relative overflow-hidden rounded-xl transition-all duration-300 ease-out',
                                    'hover:scale-[1.02] hover:shadow-lg active:scale-[0.98]',
                                    'focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2',
                                    isActive
                                        ? `bg-gradient-to-r ${colorScheme.gradient} text-white shadow-lg ${colorScheme.shadow}`
                                        : 'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground',
                                    'group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:p-2',
                                )}
                                onMouseEnter={() => setHoveredItem(item.title)}
                                onMouseLeave={() => setHoveredItem(null)}
                            >
                                <Link href={item.href} prefetch className="flex w-full items-center gap-3 p-3">
                                    {/* Animated background for hover effect */}
                                    {!isActive && (
                                        <div
                                            className={cn(
                                                'absolute inset-0 bg-gradient-to-r opacity-0 transition-opacity duration-300',
                                                colorScheme.hoverGradient,
                                                hoveredItem === item.title && 'opacity-10',
                                            )}
                                        />
                                    )}

                                    {/* Icon container with modern styling */}
                                    {item.icon && (
                                        <div
                                            className={cn(
                                                'relative flex items-center justify-center rounded-lg p-2 transition-all duration-300',
                                                isActive ? 'bg-white/20 backdrop-blur-sm' : colorScheme.iconBg,
                                                'group-data-[collapsible=icon]:p-1.5',
                                            )}
                                        >
                                            <item.icon
                                                className={cn(
                                                    'h-5 w-5 transition-all duration-300',
                                                    isActive ? 'text-white' : colorScheme.iconColor,
                                                    hoveredItem === item.title && !isActive && 'scale-110',
                                                    'group-data-[collapsible=icon]:h-4 group-data-[collapsible=icon]:w-4',
                                                )}
                                            />
                                        </div>
                                    )}

                                    {/* Text with modern typography */}
                                    <span
                                        className={cn(
                                            'truncate text-sm font-medium transition-all duration-300',
                                            isActive && 'font-semibold text-white',
                                            'group-data-[collapsible=icon]:hidden',
                                        )}
                                    >
                                        {item.title}
                                    </span>

                                    {/* Active indicator */}
                                    {isActive && <div className="absolute top-1/2 left-0 h-8 w-1 -translate-y-1/2 rounded-r-full bg-white/30" />}
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    );
                })}
            </SidebarMenu>
        </SidebarGroup>
    );
}
