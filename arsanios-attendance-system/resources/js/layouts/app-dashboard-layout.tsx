import { useRTL } from '@/contexts/rtl-context';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import React, { useEffect } from 'react';

interface AppDashboardLayoutProps {
    children: React.ReactNode;
}

export function AppDashboardLayout({ children }: AppDashboardLayoutProps) {
    const { direction } = useRTL();
    const { initializeWithMockData, initialized } = useAppStore();

    // Initialize mock data on mount
    useEffect(() => {
        if (!initialized) {
            initializeWithMockData();
        }
    }, [initialized, initializeWithMockData]);

    return (
        <div dir={direction} className={cn('min-h-screen bg-gray-50', direction)}>
            <div className="font-cairo">{children}</div>
        </div>
    );
}
