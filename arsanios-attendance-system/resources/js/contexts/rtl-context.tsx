import type React from 'react';

import { createContext, useContext, useEffect, useState } from 'react';

interface RTLContextType {
    isRTL: boolean;
    direction: 'ltr' | 'rtl';
    toggleDirection: () => void;
}

const RTLContext = createContext<RTLContextType | undefined>(undefined);

export function RTLProvider({ children }: { children: React.ReactNode }) {
    const [isRTL, setIsRTL] = useState(true); // Default to RTL for Arabic
    const direction = isRTL ? 'rtl' : 'ltr';

    useEffect(() => {
        // Set document direction
        document.documentElement.dir = direction;
        document.documentElement.lang = isRTL ? 'ar' : 'en';
    }, [direction, isRTL]);

    const toggleDirection = () => {
        setIsRTL(!isRTL);
    };

    return (
        <RTLContext.Provider value={{ isRTL, direction, toggleDirection }}>
            <div dir={direction} className={`${direction}`}>
                {children}
            </div>
        </RTLContext.Provider>
    );
}

export const useRTL = () => {
    const context = useContext(RTLContext);
    if (context === undefined) {
        throw new Error('useRTL must be used within an RTLProvider');
    }
    return context;
};
