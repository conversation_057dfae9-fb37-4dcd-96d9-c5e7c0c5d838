import { useState, useEffect, useMemo, useCallback } from 'react';
import { searchService, SearchFilters, SearchResult } from '@/lib/search';
import { User, AttendanceRecord } from '@/stores/app-store';
import { useDebounce } from './use-debounce';

interface UseSearchOptions {
    debounceMs?: number;
    minQueryLength?: number;
    enableSuggestions?: boolean;
    maxSuggestions?: number;
}

interface UseSearchReturn<T> {
    query: string;
    setQuery: (query: string) => void;
    results: SearchResult<T>[];
    isSearching: boolean;
    suggestions: string[];
    filters: SearchFilters;
    setFilters: (filters: SearchFilters) => void;
    clearSearch: () => void;
    totalResults: number;
    facets?: any;
}

// Hook for searching users
export function useUserSearch(
    users: User[],
    options: UseSearchOptions = {}
): UseSearchReturn<User> {
    const {
        debounceMs = 300,
        minQueryLength = 1,
        enableSuggestions = true,
        maxSuggestions = 5,
    } = options;

    const [query, setQuery] = useState('');
    const [filters, setFilters] = useState<SearchFilters>({});
    const [isSearching, setIsSearching] = useState(false);
    const debouncedQuery = useDebounce(query, debounceMs);

    // Initialize search service when users change
    useEffect(() => {
        if (users.length > 0) {
            searchService.initializeUsers(users);
        }
    }, [users]);

    // Perform search
    const results = useMemo(() => {
        if (!debouncedQuery || debouncedQuery.length < minQueryLength) {
            // Return all users with filters applied when no query
            return users
                .filter(user => applyUserFilters(user, filters))
                .map(user => ({ item: user }));
        }

        setIsSearching(true);
        const searchResults = searchService.searchUsers(debouncedQuery, filters);
        setIsSearching(false);
        return searchResults;
    }, [debouncedQuery, filters, users, minQueryLength]);

    // Get suggestions
    const suggestions = useMemo(() => {
        if (!enableSuggestions || !query || query.length < 2) {
            return [];
        }
        return searchService.getUserSuggestions(query, maxSuggestions);
    }, [query, enableSuggestions, maxSuggestions]);

    // Get facets for filters
    const facets = useMemo(() => {
        return searchService.getUserFacets(debouncedQuery || '*');
    }, [debouncedQuery]);

    const clearSearch = useCallback(() => {
        setQuery('');
        setFilters({});
    }, []);

    return {
        query,
        setQuery,
        results,
        isSearching,
        suggestions,
        filters,
        setFilters,
        clearSearch,
        totalResults: results.length,
        facets,
    };
}

// Hook for searching attendance records
export function useAttendanceSearch(
    attendanceRecords: AttendanceRecord[],
    options: UseSearchOptions = {}
): UseSearchReturn<AttendanceRecord> {
    const {
        debounceMs = 300,
        minQueryLength = 1,
        enableSuggestions = true,
        maxSuggestions = 5,
    } = options;

    const [query, setQuery] = useState('');
    const [filters, setFilters] = useState<SearchFilters>({});
    const [isSearching, setIsSearching] = useState(false);
    const debouncedQuery = useDebounce(query, debounceMs);

    // Initialize search service when records change
    useEffect(() => {
        if (attendanceRecords.length > 0) {
            searchService.initializeAttendance(attendanceRecords);
        }
    }, [attendanceRecords]);

    // Perform search
    const results = useMemo(() => {
        if (!debouncedQuery || debouncedQuery.length < minQueryLength) {
            // Return all records with filters applied when no query
            return attendanceRecords
                .filter(record => applyAttendanceFilters(record, filters))
                .map(record => ({ item: record }));
        }

        setIsSearching(true);
        const searchResults = searchService.searchAttendance(debouncedQuery, filters);
        setIsSearching(false);
        return searchResults;
    }, [debouncedQuery, filters, attendanceRecords, minQueryLength]);

    // Get suggestions
    const suggestions = useMemo(() => {
        if (!enableSuggestions || !query || query.length < 2) {
            return [];
        }
        return searchService.getAttendanceSuggestions(query, maxSuggestions);
    }, [query, enableSuggestions, maxSuggestions]);

    const clearSearch = useCallback(() => {
        setQuery('');
        setFilters({});
    }, []);

    return {
        query,
        setQuery,
        results,
        isSearching,
        suggestions,
        filters,
        setFilters,
        clearSearch,
        totalResults: results.length,
    };
}

// Helper function to apply user filters
function applyUserFilters(user: User, filters: SearchFilters): boolean {
    if (filters.year && filters.year !== 'all' && user.year !== filters.year) {
        return false;
    }
    if (filters.gender && filters.gender !== 'all' && user.gender !== filters.gender) {
        return false;
    }
    if (filters.college && filters.college !== 'all' && !user.college.toLowerCase().includes(filters.college.toLowerCase())) {
        return false;
    }
    if (filters.department && filters.department !== 'all' && !user.department.toLowerCase().includes(filters.department.toLowerCase())) {
        return false;
    }
    return true;
}

// Helper function to apply attendance filters
function applyAttendanceFilters(record: AttendanceRecord, filters: SearchFilters): boolean {
    if (filters.present !== undefined && filters.present !== 'all' && record.present !== filters.present) {
        return false;
    }
    if (filters.dateFrom && record.date < filters.dateFrom) {
        return false;
    }
    if (filters.dateTo && record.date > filters.dateTo) {
        return false;
    }
    if (filters.markedBy && filters.markedBy !== 'all' && !record.marked_by.toLowerCase().includes(filters.markedBy.toLowerCase())) {
        return false;
    }
    return true;
}

// Generic search hook for any data type
export function useGenericSearch<T>(
    data: T[],
    searchFn: (query: string, items: T[]) => T[],
    options: UseSearchOptions = {}
) {
    const { debounceMs = 300, minQueryLength = 1 } = options;
    
    const [query, setQuery] = useState('');
    const [isSearching, setIsSearching] = useState(false);
    const debouncedQuery = useDebounce(query, debounceMs);

    const results = useMemo(() => {
        if (!debouncedQuery || debouncedQuery.length < minQueryLength) {
            return data;
        }

        setIsSearching(true);
        const searchResults = searchFn(debouncedQuery, data);
        setIsSearching(false);
        return searchResults;
    }, [debouncedQuery, data, searchFn, minQueryLength]);

    const clearSearch = useCallback(() => {
        setQuery('');
    }, []);

    return {
        query,
        setQuery,
        results,
        isSearching,
        clearSearch,
        totalResults: results.length,
    };
}

// Hook for real-time search with instant results
export function useInstantSearch<T>(
    data: T[],
    searchFields: (keyof T)[],
    options: { caseSensitive?: boolean } = {}
) {
    const [query, setQuery] = useState('');
    const { caseSensitive = false } = options;

    const results = useMemo(() => {
        if (!query.trim()) {
            return data;
        }

        const searchTerm = caseSensitive ? query : query.toLowerCase();
        
        return data.filter(item => {
            return searchFields.some(field => {
                const fieldValue = item[field];
                if (typeof fieldValue === 'string') {
                    const value = caseSensitive ? fieldValue : fieldValue.toLowerCase();
                    return value.includes(searchTerm);
                }
                return false;
            });
        });
    }, [query, data, searchFields, caseSensitive]);

    const clearSearch = useCallback(() => {
        setQuery('');
    }, []);

    return {
        query,
        setQuery,
        results,
        clearSearch,
        totalResults: results.length,
    };
}
