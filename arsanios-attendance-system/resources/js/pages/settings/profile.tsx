'use client';

import { type BreadcrumbItem, type SharedData } from '@/types';
import { Transition } from '@headlessui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { useForm as useReactHookForm } from 'react-hook-form';
import * as z from 'zod';

import DeleteUser from '@/components/delete-user';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useRTL } from '@/contexts/rtl-context';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import { Bell, Clock, Database, Globe, MessageSquare, Palette, Save, Settings, Shield, User, Users } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'إعدادات الملف الشخصي',
        href: '/settings/profile',
    },
];

type ProfileForm = {
    name: string;
    email: string;
};

const settingsSchema = z.object({
    // Attendance Settings
    auto_mark_absent: z.boolean().default(false),
    absent_cutoff_time: z.string().min(1, 'وقت الحد الأقصى مطلوب'),

    // Notification Settings
    birthday_notifications: z.boolean().default(true),
    notification_frequency: z.enum(['weekly', 'monthly', 'end_of_month']),
    whatsapp_message_template: z.string().min(10, 'نموذج الرسالة يجب أن يكون أكثر تفصيلاً'),

    // System Settings
    organization_name: z.string().min(2, 'اسم المنظمة مطلوب'),
    organization_logo: z.string().optional(),
    primary_color: z.string().default('#3B82F6'),
    secondary_color: z.string().default('#10B981'),

    // Security Settings
    require_approval: z.boolean().default(false),
    max_login_attempts: z.number().min(1).max(10).default(5),
    session_timeout: z.number().min(15).max(1440).default(60),

    // Data Settings
    backup_frequency: z.enum(['daily', 'weekly', 'monthly']),
    data_retention_days: z.number().min(30).max(3650).default(365),
});

type SettingsData = z.infer<typeof settingsSchema>;

export default function Profile({ mustVerifyEmail, status }: { mustVerifyEmail: boolean; status?: string }) {
    const { auth } = usePage<SharedData>().props;
    const { settings, updateSettings } = useAppStore();
    const { isRTL, direction } = useRTL();
    const [activeTab, setActiveTab] = useState('profile');
    const [isSaving, setIsSaving] = useState(false);

    // Profile form
    const { data, setData, patch, errors, processing, recentlySuccessful } = useForm<Required<ProfileForm>>({
        name: auth.user.name,
        email: auth.user.email,
    });

    // System settings form
    const form = useReactHookForm<SettingsData>({
        resolver: zodResolver(settingsSchema),
        defaultValues: {
            auto_mark_absent: settings.auto_mark_absent || false,
            absent_cutoff_time: settings.absent_cutoff_time || '18:00',
            birthday_notifications: settings.birthday_notifications !== false,
            notification_frequency: settings.notification_frequency || 'monthly',
            whatsapp_message_template: settings.whatsapp_message_template || '🎉 عيد ميلاد سعيد {name}! نتمنى لك عامًا جديدًا مليئًا بالفرح والنجاح.',
            organization_name: 'نظام حضور الشباب',
            organization_logo: '',
            primary_color: '#3B82F6',
            secondary_color: '#10B981',
            require_approval: false,
            max_login_attempts: 5,
            session_timeout: 60,
            backup_frequency: 'weekly',
            data_retention_days: 365,
        },
    });

    const submitProfile: FormEventHandler = (e) => {
        e.preventDefault();
        patch(route('profile.update'), {
            preserveScroll: true,
        });
    };

    const onSubmitSettings = async (data: SettingsData) => {
        setIsSaving(true);
        try {
            await updateSettings({
                auto_mark_absent: data.auto_mark_absent,
                absent_cutoff_time: data.absent_cutoff_time,
                birthday_notifications: data.birthday_notifications,
                notification_frequency: data.notification_frequency,
                whatsapp_message_template: data.whatsapp_message_template,
            });

            console.log('Settings updated:', data);
            alert('تم حفظ الإعدادات بنجاح!');
        } catch (error) {
            console.error('Error saving settings:', error);
            alert('حدث خطأ أثناء حفظ الإعدادات');
        } finally {
            setIsSaving(false);
        }
    };

    const settingsTabs = [
        { id: 'profile', label: 'الملف الشخصي', icon: User },
        { id: 'attendance', label: 'الحضور', icon: Users },
        { id: 'notifications', label: 'الإشعارات', icon: Bell },
        { id: 'appearance', label: 'المظهر', icon: Palette },
        { id: 'security', label: 'الحماية', icon: Shield },
        { id: 'data', label: 'البيانات', icon: Database },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="إعدادات الملف الشخصي" />

            <SettingsLayout>
                <div className="font-cairo space-y-6" dir={direction}>
                    {/* Header */}
                    <div className="animate-fade-in">
                        <div className={cn('mb-2 flex items-center gap-3', isRTL ? 'flex-row-reverse' : 'flex-row')}>
                            <div className="rounded-lg bg-gradient-to-r from-gray-500 to-slate-500 p-2">
                                <Settings className="h-6 w-6 text-white" />
                            </div>
                            <h1 className="text-heading gradient-text text-4xl font-bold">إعدادات النظام</h1>
                        </div>
                        <p className={cn('text-body text-lg text-gray-600', isRTL ? 'text-right' : 'text-left')}>تخصيص وإعداد النظام حسب احتياجاتك</p>
                    </div>

                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
                        {/* Settings Navigation */}
                        <Card className="animate-scale-in h-fit lg:col-span-1">
                            <CardHeader>
                                <CardTitle className="text-lg">أقسام الإعدادات</CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                <nav className="space-y-1">
                                    {settingsTabs.map((tab) => (
                                        <button
                                            key={tab.id}
                                            onClick={() => setActiveTab(tab.id)}
                                            className={cn(
                                                'flex w-full items-center gap-3 px-4 py-3 text-right transition-colors',
                                                activeTab === tab.id
                                                    ? 'border-r-2 border-blue-500 bg-blue-50 text-blue-700'
                                                    : 'text-gray-600 hover:bg-gray-50',
                                            )}
                                        >
                                            <tab.icon className="h-4 w-4" />
                                            <span>{tab.label}</span>
                                        </button>
                                    ))}
                                </nav>
                            </CardContent>
                        </Card>

                        {/* Settings Content */}
                        <div className="animate-slide-in-right lg:col-span-3">
                            {/* Profile Settings */}
                            {activeTab === 'profile' && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <User className="h-5 w-5" />
                                            معلومات الملف الشخصي
                                        </CardTitle>
                                        <CardDescription>تحديث اسمك وعنوان بريدك الإلكتروني</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <form onSubmit={submitProfile} className="space-y-6">
                                            <div className="grid gap-2">
                                                <Label htmlFor="name">الاسم</Label>
                                                <Input
                                                    id="name"
                                                    className="mt-1 block w-full"
                                                    value={data.name}
                                                    onChange={(e) => setData('name', e.target.value)}
                                                    required
                                                    autoComplete="name"
                                                    placeholder="الاسم الكامل"
                                                />
                                                <InputError className="mt-2" message={errors.name} />
                                            </div>

                                            <div className="grid gap-2">
                                                <Label htmlFor="email">عنوان البريد الإلكتروني</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    className="mt-1 block w-full"
                                                    value={data.email}
                                                    onChange={(e) => setData('email', e.target.value)}
                                                    required
                                                    autoComplete="username"
                                                    placeholder="عنوان البريد الإلكتروني"
                                                />
                                                <InputError className="mt-2" message={errors.email} />
                                            </div>

                                            {mustVerifyEmail && auth.user.email_verified_at === null && (
                                                <div>
                                                    <p className="-mt-4 text-sm text-muted-foreground">
                                                        عنوان بريدك الإلكتروني غير موثق.{' '}
                                                        <Link
                                                            href={route('verification.send')}
                                                            method="post"
                                                            as="button"
                                                            className="text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"
                                                        >
                                                            انقر هنا لإعادة إرسال بريد التحقق.
                                                        </Link>
                                                    </p>

                                                    {status === 'verification-link-sent' && (
                                                        <div className="mt-2 text-sm font-medium text-green-600">
                                                            تم إرسال رابط تحقق جديد إلى عنوان بريدك الإلكتروني.
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            <div className="flex items-center gap-4">
                                                <Button disabled={processing}>حفظ</Button>
                                                <Transition
                                                    show={recentlySuccessful}
                                                    enter="transition ease-in-out"
                                                    enterFrom="opacity-0"
                                                    leave="transition ease-in-out"
                                                    leaveTo="opacity-0"
                                                >
                                                    <p className="text-sm text-neutral-600">تم الحفظ</p>
                                                </Transition>
                                            </div>
                                        </form>

                                        <DeleteUser />
                                    </CardContent>
                                </Card>
                            )}

                            {/* System Settings Form */}
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmitSettings)} className="space-y-6">
                                    {/* Attendance Settings */}
                                    {activeTab === 'attendance' && (
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                    <Users className="h-5 w-5" />
                                                    إعدادات الحضور
                                                </CardTitle>
                                                <CardDescription>تخصيص كيفية عمل نظام تسجيل الحضور</CardDescription>
                                            </CardHeader>
                                            <CardContent className="space-y-6">
                                                <FormField
                                                    control={form.control}
                                                    name="auto_mark_absent"
                                                    render={({ field }) => (
                                                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                                            <div className="space-y-0.5">
                                                                <FormLabel className="text-base">تسجيل الغياب التلقائي</FormLabel>
                                                                <FormDescription>
                                                                    تسجيل الأعضاء الذين لم يحضروا تلقائياً كغائبين بعد الوقت المحدد
                                                                </FormDescription>
                                                            </div>
                                                            <FormControl>
                                                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="absent_cutoff_time"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel className="flex items-center gap-2">
                                                                <Clock className="h-4 w-4" />
                                                                الوقت الحد الأقصى للحضور
                                                            </FormLabel>
                                                            <FormControl>
                                                                <Input type="time" {...field} />
                                                            </FormControl>
                                                            <FormDescription>الوقت الذي بعده يتم تسجيل الأعضاء كغائبين تلقائياً</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Notifications Settings */}
                                    {activeTab === 'notifications' && (
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                    <Bell className="h-5 w-5" />
                                                    إعدادات الإشعارات
                                                </CardTitle>
                                                <CardDescription>تخصيص الإشعارات والرسائل التلقائية</CardDescription>
                                            </CardHeader>
                                            <CardContent className="space-y-6">
                                                <FormField
                                                    control={form.control}
                                                    name="birthday_notifications"
                                                    render={({ field }) => (
                                                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                                            <div className="space-y-0.5">
                                                                <FormLabel className="text-base">إشعارات أعياد الميلاد</FormLabel>
                                                                <FormDescription>إرسال إشعارات عند حلول أعياد ميلاد الأعضاء</FormDescription>
                                                            </div>
                                                            <FormControl>
                                                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="notification_frequency"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>تكرار الإشعارات</FormLabel>
                                                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                                <FormControl>
                                                                    <SelectTrigger>
                                                                        <SelectValue placeholder="اختر تكرار الإشعارات" />
                                                                    </SelectTrigger>
                                                                </FormControl>
                                                                <SelectContent>
                                                                    <SelectItem value="weekly">أسبوعياً</SelectItem>
                                                                    <SelectItem value="monthly">شهرياً</SelectItem>
                                                                    <SelectItem value="end_of_month">نهاية الشهر</SelectItem>
                                                                </SelectContent>
                                                            </Select>
                                                            <FormDescription>متى تريد إرسال تقارير الحضور والإشعارات</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="whatsapp_message_template"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel className="flex items-center gap-2">
                                                                <MessageSquare className="h-4 w-4" />
                                                                نموذج رسالة واتساب
                                                            </FormLabel>
                                                            <FormControl>
                                                                <Textarea
                                                                    placeholder="اكتب نموذج الرسالة..."
                                                                    className="resize-none"
                                                                    rows={4}
                                                                    {...field}
                                                                />
                                                            </FormControl>
                                                            <FormDescription>استخدم {'{name}'} لإدراج اسم العضو في الرسالة</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Appearance Settings */}
                                    {activeTab === 'appearance' && (
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                    <Palette className="h-5 w-5" />
                                                    إعدادات المظهر
                                                </CardTitle>
                                                <CardDescription>تخصيص شكل ومظهر النظام</CardDescription>
                                            </CardHeader>
                                            <CardContent className="space-y-6">
                                                <FormField
                                                    control={form.control}
                                                    name="organization_name"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel className="flex items-center gap-2">
                                                                <Globe className="h-4 w-4" />
                                                                اسم المنظمة
                                                            </FormLabel>
                                                            <FormControl>
                                                                <Input placeholder="اسم المنظمة أو الجهة" {...field} />
                                                            </FormControl>
                                                            <FormDescription>الاسم الذي سيظهر في رأس الصفحة والتقارير</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="primary_color"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>اللون الأساسي</FormLabel>
                                                            <FormControl>
                                                                <div className="flex items-center gap-3">
                                                                    <Input type="color" {...field} className="h-10 w-16 rounded border p-1" />
                                                                    <Input {...field} placeholder="#3B82F6" />
                                                                </div>
                                                            </FormControl>
                                                            <FormDescription>اللون الأساسي للواجهة والأزرار</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="secondary_color"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>اللون الثانوي</FormLabel>
                                                            <FormControl>
                                                                <div className="flex items-center gap-3">
                                                                    <Input type="color" {...field} className="h-10 w-16 rounded border p-1" />
                                                                    <Input {...field} placeholder="#10B981" />
                                                                </div>
                                                            </FormControl>
                                                            <FormDescription>اللون المستخدم للعناصر الثانوية والتأكيدات</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Security Settings */}
                                    {activeTab === 'security' && (
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                    <Shield className="h-5 w-5" />
                                                    إعدادات الحماية
                                                </CardTitle>
                                                <CardDescription>تأمين النظام وحماية البيانات</CardDescription>
                                            </CardHeader>
                                            <CardContent className="space-y-6">
                                                <FormField
                                                    control={form.control}
                                                    name="require_approval"
                                                    render={({ field }) => (
                                                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                                            <div className="space-y-0.5">
                                                                <FormLabel className="text-base">مطالبة بالموافقة</FormLabel>
                                                                <FormDescription>مطالبة المدير بالموافقة على العمليات الحساسة</FormDescription>
                                                            </div>
                                                            <FormControl>
                                                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                                                            </FormControl>
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="max_login_attempts"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>عدد محاولات تسجيل الدخول</FormLabel>
                                                            <FormControl>
                                                                <Input
                                                                    type="number"
                                                                    min="1"
                                                                    max="10"
                                                                    {...field}
                                                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                                                />
                                                            </FormControl>
                                                            <FormDescription>عدد المحاولات المسموحة قبل حظر الحساب مؤقتاً</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="session_timeout"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>مهلة انتهاء الجلسة (بالدقائق)</FormLabel>
                                                            <FormControl>
                                                                <Input
                                                                    type="number"
                                                                    min="15"
                                                                    max="1440"
                                                                    {...field}
                                                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                                                />
                                                            </FormControl>
                                                            <FormDescription>المدة بالدقائق قبل انتهاء صلاحية الجلسة تلقائياً</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Data Settings */}
                                    {activeTab === 'data' && (
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                    <Database className="h-5 w-5" />
                                                    إعدادات البيانات
                                                </CardTitle>
                                                <CardDescription>إدارة النسخ الاحتياطي والاحتفاظ بالبيانات</CardDescription>
                                            </CardHeader>
                                            <CardContent className="space-y-6">
                                                <FormField
                                                    control={form.control}
                                                    name="backup_frequency"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>تكرار النسخ الاحتياطي</FormLabel>
                                                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                                <FormControl>
                                                                    <SelectTrigger>
                                                                        <SelectValue placeholder="اختر تكرار النسخ الاحتياطي" />
                                                                    </SelectTrigger>
                                                                </FormControl>
                                                                <SelectContent>
                                                                    <SelectItem value="daily">يومياً</SelectItem>
                                                                    <SelectItem value="weekly">أسبوعياً</SelectItem>
                                                                    <SelectItem value="monthly">شهرياً</SelectItem>
                                                                </SelectContent>
                                                            </Select>
                                                            <FormDescription>كم مرة تريد إنشاء نسخة احتياطية من البيانات</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                <FormField
                                                    control={form.control}
                                                    name="data_retention_days"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>مدة الاحتفاظ بالبيانات (بالأيام)</FormLabel>
                                                            <FormControl>
                                                                <Input
                                                                    type="number"
                                                                    min="30"
                                                                    max="3650"
                                                                    {...field}
                                                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                                                />
                                                            </FormControl>
                                                            <FormDescription>عدد الأيام للاحتفاظ بسجلات الحضور والبيانات القديمة</FormDescription>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                                                    <h4 className="mb-2 font-medium text-yellow-800">تنبيه مهم</h4>
                                                    <p className="text-sm text-yellow-700">
                                                        تأكد من إجراء نسخة احتياطية قبل تغيير إعدادات البيانات. حذف البيانات القديمة لا يمكن التراجع
                                                        عنه.
                                                    </p>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Save Button for System Settings */}
                                    {activeTab !== 'profile' && (
                                        <div className="flex justify-end pt-6">
                                            <Button type="submit" className="btn-gradient px-8 py-3 text-lg" disabled={isSaving}>
                                                <Save className="mr-2 h-5 w-5" />
                                                {isSaving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
                                            </Button>
                                        </div>
                                    )}
                                </form>
                            </Form>
                        </div>
                    </div>
                </div>
            </SettingsLayout>
        </AppLayout>
    );
}
