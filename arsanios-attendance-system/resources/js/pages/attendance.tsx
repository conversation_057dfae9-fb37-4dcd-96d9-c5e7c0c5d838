'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/auth-context';
import { useRTL } from '@/contexts/rtl-context';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import { endOfWeek, format, startOfWeek } from 'date-fns';
import { ar } from 'date-fns/locale';
import {
    BarChart3,
    Calendar,
    Camera,
    CheckCircle,
    ClipboardCheck,
    Clock,
    Download,
    Eye,
    GraduationCap,
    Phone,
    QrCode,
    Search,
    Sparkles,
    TrendingUp,
    UserCheck,
    Users,
    X,
} from 'lucide-react';
import { useMemo, useState } from 'react';

function AttendancePage() {
    const { users, attendanceRecords, markAttendance, bulkMarkAttendance, exportAttendance } = useAppStore();
    const { user } = useAuth();
    const { isRTL, direction } = useRTL();

    const [searchQuery, setSearchQuery] = useState('');
    const [selectedUser, setSelectedUser] = useState<string>('');
    const [manualId, setManualId] = useState('');
    const [recentAttendance, setRecentAttendance] = useState<string[]>([]);
    const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
    const [filterYear, setFilterYear] = useState('all');
    const [filterCollege, setFilterCollege] = useState('all');
    const [showAttendanceDetails, setShowAttendanceDetails] = useState(false);

    const today = new Date();
    const todayString = today.toISOString().split('T')[0];

    // Filter users based on search and filters
    const filteredUsers = useMemo(() => {
        return users.filter((u) => {
            const matchesSearch = u.name.toLowerCase().includes(searchQuery.toLowerCase()) || u.phone.includes(searchQuery);
            const matchesYear = filterYear === 'all' || u.year.toString() === filterYear;
            const matchesCollege = filterCollege === 'all' || u.college.includes(filterCollege);

            return matchesSearch && matchesYear && matchesCollege;
        });
    }, [users, searchQuery, filterYear, filterCollege]);

    // Get attendance for selected date
    const dateAttendance = useMemo(() => {
        return attendanceRecords.filter((record) => record.date === selectedDate);
    }, [attendanceRecords, selectedDate]);

    // Get today's attendance status
    const getTodayAttendance = () => {
        const present = dateAttendance.filter((a) => a.present).length;
        const absent = users.length - present;
        const attendanceRate = users.length > 0 ? Math.round((present / users.length) * 100) : 0;
        return { present, absent, total: users.length, attendanceRate };
    };

    const attendanceStats = getTodayAttendance();

    // Get weekly attendance summary
    const weeklyStats = useMemo(() => {
        const weekStart = startOfWeek(today, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(today, { weekStartsOn: 1 });

        const weekRecords = attendanceRecords.filter((record) => {
            const recordDate = new Date(record.date);
            return recordDate >= weekStart && recordDate <= weekEnd;
        });

        const weeklyPresent = weekRecords.filter((r) => r.present).length;
        const weeklyTotal = weekRecords.length;
        const weeklyRate = weeklyTotal > 0 ? Math.round((weeklyPresent / weeklyTotal) * 100) : 0;

        return { weeklyPresent, weeklyTotal, weeklyRate };
    }, [attendanceRecords, today]);

    const handleMarkAttendance = (userId: string, present = true) => {
        if (!user?.email) return;

        markAttendance(userId, present, user.email);
        setRecentAttendance((prev) => [userId, ...prev.slice(0, 4)]);
        setSearchQuery('');
        setSelectedUser('');
        setManualId('');
    };

    const isUserPresent = (userId: string) => {
        return dateAttendance.some((a) => a.user_id === userId && a.present);
    };

    const isUserAbsent = (userId: string) => {
        return dateAttendance.some((a) => a.user_id === userId && !a.present);
    };

    const handleManualIdSubmit = () => {
        const foundUser = users.find((u) => u.id === manualId || u.phone === manualId);
        if (foundUser) {
            handleMarkAttendance(foundUser.id);
        }
    };

    const handleBulkMarkPresent = () => {
        const userIds = filteredUsers.map((u) => u.id);
        bulkMarkAttendance(userIds, true, user?.email || 'admin');
    };

    const handleExportAttendance = (format: 'csv' | 'excel' | 'pdf') => {
        exportAttendance(format, {
            dateFrom: selectedDate,
            dateTo: selectedDate,
        });
    };

    const colleges = [...new Set(users.map((u) => u.college))];

    return (
        <div className="page-transition font-cairo space-y-6 p-6" dir={direction}>
            {/* Enhanced Header */}
            <div className="animate-fade-in">
                <div className={cn('flex items-center justify-between', 'flex-row')}>
                    <div>
                        <div className={cn('mb-2 flex items-center gap-3', 'flex-row')}>
                            <div className="rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 p-2">
                                <ClipboardCheck className="h-6 w-6 text-white" />
                            </div>
                            <h1 className="gradient-text text-4xl font-bold py-1">تسجيل الحضور</h1>
                            <Sparkles className="h-6 w-6 animate-pulse text-yellow-500" />
                        </div>
                        <p className={cn('text-body font-cairo text-lg text-gray-600', isRTL ? 'text-right' : 'text-left')}>
                            تسجيل حضور الأعضاء - {format(new Date(selectedDate), 'EEEE، dd MMMM yyyy', { locale: ar })}
                        </p>
                    </div>

                    <div className="flex gap-2">
                        <Button variant="outline" onClick={() => setShowAttendanceDetails(true)}>
                            <Eye className="mr-2 h-4 w-4" />
                            تفاصيل الحضور
                        </Button>
                        <Button variant="outline" onClick={() => handleExportAttendance('csv')}>
                            <Download className="mr-2 h-4 w-4" />
                            تصدير
                        </Button>
                    </div>
                </div>
            </div>

            {/* Date Selection */}
            <Card className="animate-scale-in">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        اختيار التاريخ
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                        <div>
                            <label className="mb-2 block text-sm font-medium">التاريخ</label>
                            <Input type="date" value={selectedDate} onChange={(e) => setSelectedDate(e.target.value)} className="w-full" />
                        </div>
                        <div>
                            <label className="mb-2 block text-sm font-medium">السنة الدراسية</label>
                            <Select value={filterYear} onValueChange={setFilterYear}>
                                <SelectTrigger>
                                    <SelectValue placeholder="جميع السنوات" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">جميع السنوات</SelectItem>
                                    <SelectItem value="1">السنة الأولى</SelectItem>
                                    <SelectItem value="2">السنة الثانية</SelectItem>
                                    <SelectItem value="3">السنة الثالثة</SelectItem>
                                    <SelectItem value="4">السنة الرابعة</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <label className="mb-2 block text-sm font-medium">الكلية</label>
                            <Select value={filterCollege} onValueChange={setFilterCollege}>
                                <SelectTrigger>
                                    <SelectValue placeholder="جميع الكليات" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">جميع الكليات</SelectItem>
                                    {colleges.map((college) => (
                                        <SelectItem key={college} value={college}>
                                            {college}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-end">
                            <Button onClick={handleBulkMarkPresent} className="btn-gradient w-full">
                                <UserCheck className="mr-2 h-4 w-4" />
                                تسجيل الكل حاضر
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Enhanced Stats Cards */}
            <div className="animate-scale-in grid grid-cols-1 gap-6 md:grid-cols-4">
                <Card className="hover-lift">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">إجمالي الأعضاء</CardTitle>
                        <Users className="h-4 w-4 text-blue-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{attendanceStats.total}</div>
                        <p className="text-xs text-muted-foreground">عضو مسجل</p>
                    </CardContent>
                </Card>

                <Card className="hover-lift">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">الحاضرون</CardTitle>
                        <UserCheck className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">{attendanceStats.present}</div>
                        <p className="text-xs text-muted-foreground">{attendanceStats.attendanceRate}% معدل الحضور</p>
                    </CardContent>
                </Card>

                <Card className="hover-lift">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">الغائبون</CardTitle>
                        <Clock className="h-4 w-4 text-orange-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-orange-600">{attendanceStats.absent}</div>
                        <p className="text-xs text-muted-foreground">عضو غائب</p>
                    </CardContent>
                </Card>

                <Card className="hover-lift">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">المعدل الأسبوعي</CardTitle>
                        <TrendingUp className="h-4 w-4 text-purple-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-purple-600">{weeklyStats.weeklyRate}%</div>
                        <p className="text-xs text-muted-foreground">
                            {weeklyStats.weeklyPresent} من {weeklyStats.weeklyTotal}
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Enhanced Attendance Methods */}
            <Card className="animate-slide-in-right">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        طرق تسجيل الحضور
                    </CardTitle>
                    <CardDescription>اختر الطريقة المناسبة لتسجيل الحضور</CardDescription>
                </CardHeader>
                <CardContent>
                    <Tabs defaultValue="search" className="w-full">
                        <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="search" className="flex items-center gap-2">
                                <Search className="h-4 w-4" />
                                البحث
                            </TabsTrigger>
                            <TabsTrigger value="manual" className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                إدخال يدوي
                            </TabsTrigger>
                            <TabsTrigger value="qr" className="flex items-center gap-2">
                                <QrCode className="h-4 w-4" />
                                مسح QR
                            </TabsTrigger>
                            <TabsTrigger value="list" className="flex items-center gap-2">
                                <ClipboardCheck className="h-4 w-4" />
                                قائمة شاملة
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="search" className="space-y-4">
                            <div className="space-y-4">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                    <Input
                                        placeholder="ابحث بالاسم أو رقم الهاتف..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="h-12 pl-10"
                                    />
                                </div>

                                {searchQuery && (
                                    <div className="max-h-60 space-y-2 overflow-y-auto">
                                        {filteredUsers.map((user) => (
                                            <div
                                                key={user.id}
                                                className={cn(
                                                    'flex cursor-pointer items-center justify-between rounded-lg border p-3 transition-colors',
                                                    isUserPresent(user.id)
                                                        ? 'border-green-200 bg-green-50'
                                                        : isUserAbsent(user.id)
                                                          ? 'border-red-200 bg-red-50'
                                                          : 'hover:bg-gray-50',
                                                )}
                                                onClick={() => !isUserPresent(user.id) && handleMarkAttendance(user.id)}
                                            >
                                                <div className="flex items-center gap-3">
                                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
                                                        <span className="text-sm font-bold text-white">{user.name.charAt(0)}</span>
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{user.name}</div>
                                                        <div className="flex items-center gap-2 text-sm text-gray-500">
                                                            <span>السنة {user.year}</span>
                                                            <span>•</span>
                                                            <Phone className="h-3 w-3" />
                                                            <span>{user.phone}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                {isUserPresent(user.id) ? (
                                                    <Badge className="bg-green-100 text-green-700">
                                                        <CheckCircle className="mr-1 h-3 w-3" />
                                                        حاضر
                                                    </Badge>
                                                ) : isUserAbsent(user.id) ? (
                                                    <Badge variant="destructive">
                                                        <X className="mr-1 h-3 w-3" />
                                                        غائب
                                                    </Badge>
                                                ) : (
                                                    <div className="flex gap-1">
                                                        <Button
                                                            size="sm"
                                                            className="btn-gradient"
                                                            onClick={() => handleMarkAttendance(user.id, true)}
                                                        >
                                                            حاضر
                                                        </Button>
                                                        <Button size="sm" variant="outline" onClick={() => handleMarkAttendance(user.id, false)}>
                                                            غائب
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                        {filteredUsers.length === 0 && <div className="py-4 text-center text-gray-500">لا توجد نتائج للبحث</div>}
                                    </div>
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="manual" className="space-y-4">
                            <div className="space-y-4">
                                <Input
                                    placeholder="أدخل رقم الهاتف أو معرف العضو..."
                                    value={manualId}
                                    onChange={(e) => setManualId(e.target.value)}
                                    className="h-12"
                                />
                                <div className="flex gap-2">
                                    <Button onClick={handleManualIdSubmit} disabled={!manualId} className="btn-gradient h-12 flex-1">
                                        تسجيل حضور
                                    </Button>
                                    <Button
                                        onClick={() => {
                                            const foundUser = users.find((u) => u.id === manualId || u.phone === manualId);
                                            if (foundUser) handleMarkAttendance(foundUser.id, false);
                                        }}
                                        disabled={!manualId}
                                        variant="outline"
                                        className="h-12 flex-1"
                                    >
                                        تسجيل غياب
                                    </Button>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="qr" className="space-y-4">
                            <div className="space-y-4 text-center">
                                <div className="mx-auto flex h-48 w-48 items-center justify-center rounded-lg bg-gray-100">
                                    <Camera className="h-24 w-24 text-gray-400" />
                                </div>
                                <p className="text-gray-600">اضغط لفتح الكاميرا ومسح QR Code</p>
                                <Button className="btn-gradient">
                                    <Camera className="mr-2 h-4 w-4" />
                                    فتح الكاميرا
                                </Button>
                            </div>
                        </TabsContent>

                        <TabsContent value="list" className="space-y-4">
                            <div className="max-h-96 space-y-3 overflow-y-auto">
                                {filteredUsers.map((user) => (
                                    <div
                                        key={user.id}
                                        className={cn(
                                            'flex items-center justify-between rounded-lg border p-3 transition-colors',
                                            isUserPresent(user.id)
                                                ? 'border-green-200 bg-green-50'
                                                : isUserAbsent(user.id)
                                                  ? 'border-red-200 bg-red-50'
                                                  : 'hover:bg-gray-50',
                                        )}
                                    >
                                        <div className="flex items-center gap-3">
                                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
                                                <span className="text-sm font-bold text-white">{user.name.charAt(0)}</span>
                                            </div>
                                            <div>
                                                <div className="font-medium">{user.name}</div>
                                                <div className="flex items-center gap-2 text-sm text-gray-500">
                                                    <GraduationCap className="h-3 w-3" />
                                                    <span>{user.college}</span>
                                                    <span>•</span>
                                                    <span>السنة {user.year}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            {isUserPresent(user.id) ? (
                                                <Badge className="bg-green-100 text-green-700">
                                                    <CheckCircle className="mr-1 h-3 w-3" />
                                                    حاضر
                                                </Badge>
                                            ) : isUserAbsent(user.id) ? (
                                                <Badge variant="destructive">
                                                    <X className="mr-1 h-3 w-3" />
                                                    غائب
                                                </Badge>
                                            ) : (
                                                <div className="flex gap-1">
                                                    <Button size="sm" className="btn-gradient" onClick={() => handleMarkAttendance(user.id, true)}>
                                                        حاضر
                                                    </Button>
                                                    <Button size="sm" variant="outline" onClick={() => handleMarkAttendance(user.id, false)}>
                                                        غائب
                                                    </Button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </TabsContent>
                    </Tabs>
                </CardContent>
            </Card>

            {/* Recent Attendance */}
            {recentAttendance.length > 0 && (
                <Card className="animate-fade-in">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Clock className="h-5 w-5" />
                            آخر التسجيلات
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2">
                            {recentAttendance.map((userId) => {
                                const user = users.find((u) => u.id === userId);
                                if (!user) return null;

                                return (
                                    <div key={userId} className="flex items-center gap-3 rounded-lg bg-green-50 p-2">
                                        <CheckCircle className="h-5 w-5 text-green-600" />
                                        <span className="font-medium">{user.name}</span>
                                        <Badge variant="outline">تم التسجيل</Badge>
                                        <span className="mr-auto text-xs text-gray-500">{format(new Date(), 'HH:mm')}</span>
                                    </div>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Attendance Details Modal */}
            {showAttendanceDetails && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <Card className="animate-scale-in max-h-[90vh] w-full max-w-4xl overflow-y-auto">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    تفاصيل الحضور - {format(new Date(selectedDate), 'dd MMMM yyyy', { locale: ar })}
                                </CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowAttendanceDetails(false)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-3">
                                <div className="rounded-lg bg-green-50 p-4 text-center">
                                    <div className="text-2xl font-bold text-green-600">{attendanceStats.present}</div>
                                    <div className="text-sm text-green-700">حاضر</div>
                                </div>
                                <div className="rounded-lg bg-red-50 p-4 text-center">
                                    <div className="text-2xl font-bold text-red-600">{attendanceStats.absent}</div>
                                    <div className="text-sm text-red-700">غائب</div>
                                </div>
                                <div className="rounded-lg bg-blue-50 p-4 text-center">
                                    <div className="text-2xl font-bold text-blue-600">{attendanceStats.attendanceRate}%</div>
                                    <div className="text-sm text-blue-700">معدل الحضور</div>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <h3 className="text-lg font-semibold">قائمة الحضور التفصيلية</h3>
                                {dateAttendance.map((record) => (
                                    <div key={record.id} className="flex items-center justify-between rounded-lg border p-3">
                                        <div className="flex items-center gap-3">
                                            <div className={cn('h-3 w-3 rounded-full', record.present ? 'bg-green-500' : 'bg-red-500')} />
                                            <span className="font-medium">{record.user_name}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge variant={record.present ? 'default' : 'destructive'}>{record.present ? 'حاضر' : 'غائب'}</Badge>
                                            <span className="text-xs text-gray-500">{format(new Date(record.created_at), 'HH:mm')}</span>
                                        </div>
                                    </div>
                                ))}
                                {dateAttendance.length === 0 && <div className="py-8 text-center text-gray-500">لا توجد سجلات حضور لهذا التاريخ</div>}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    );
}

AttendancePage.layout = (page: React.ReactElement) => <AppLayout children={page} />;

export default AttendancePage;
