import Fuse from 'fuse.js';
import { User, AttendanceRecord } from '@/stores/app-store';

// Search configuration for users
const userSearchOptions: Fuse.IFuseOptions<User> = {
    keys: [
        { name: 'name', weight: 0.4 },
        { name: 'phone', weight: 0.3 },
        { name: 'college', weight: 0.2 },
        { name: 'department', weight: 0.1 },
    ],
    threshold: 0.3, // Lower = more strict matching
    distance: 100,
    includeScore: true,
    includeMatches: true,
    minMatchCharLength: 1,
    shouldSort: true,
    findAllMatches: true,
    ignoreLocation: true,
};

// Search configuration for attendance records
const attendanceSearchOptions: Fuse.IFuseOptions<AttendanceRecord> = {
    keys: [
        { name: 'user_name', weight: 0.5 },
        { name: 'marked_by', weight: 0.3 },
        { name: 'date', weight: 0.2 },
    ],
    threshold: 0.4,
    distance: 100,
    includeScore: true,
    includeMatches: true,
    minMatchCharLength: 1,
    shouldSort: true,
    findAllMatches: true,
    ignoreLocation: true,
};

// Search result interface
export interface SearchResult<T> {
    item: T;
    score?: number;
    matches?: Fuse.FuseResultMatch[];
}

// Search filters interface
export interface SearchFilters {
    year?: number | 'all';
    gender?: 'male' | 'female' | 'all';
    college?: string | 'all';
    department?: string | 'all';
    present?: boolean | 'all';
    dateFrom?: string;
    dateTo?: string;
    markedBy?: string | 'all';
}

// Main search service class
export class SearchService {
    private usersFuse: Fuse<User> | null = null;
    private attendanceFuse: Fuse<AttendanceRecord> | null = null;
    private users: User[] = [];
    private attendanceRecords: AttendanceRecord[] = [];

    // Initialize search indices
    initializeUsers(users: User[]) {
        this.users = users;
        this.usersFuse = new Fuse(users, userSearchOptions);
    }

    initializeAttendance(records: AttendanceRecord[]) {
        this.attendanceRecords = records;
        this.attendanceFuse = new Fuse(records, attendanceSearchOptions);
    }

    // Search users with filters
    searchUsers(query: string, filters: SearchFilters = {}): SearchResult<User>[] {
        let results: User[] = [];

        if (query.trim() === '' || query === '*') {
            // Return all users if no query
            results = this.users;
        } else if (this.usersFuse) {
            // Perform fuzzy search
            const fuseResults = this.usersFuse.search(query);
            results = fuseResults.map(result => result.item);
        }

        // Apply filters
        const filteredResults = this.applyUserFilters(results, filters);

        // Convert to SearchResult format with highlighting
        return filteredResults.map(user => {
            const fuseResult = this.usersFuse?.search(query).find(r => r.item.id === user.id);
            return {
                item: user,
                score: fuseResult?.score,
                matches: fuseResult?.matches,
            };
        });
    }

    // Search attendance records with filters
    searchAttendance(query: string, filters: SearchFilters = {}): SearchResult<AttendanceRecord>[] {
        let results: AttendanceRecord[] = [];

        if (query.trim() === '' || query === '*') {
            results = this.attendanceRecords;
        } else if (this.attendanceFuse) {
            const fuseResults = this.attendanceFuse.search(query);
            results = fuseResults.map(result => result.item);
        }

        // Apply filters
        const filteredResults = this.applyAttendanceFilters(results, filters);

        return filteredResults.map(record => {
            const fuseResult = this.attendanceFuse?.search(query).find(r => r.item.id === record.id);
            return {
                item: record,
                score: fuseResult?.score,
                matches: fuseResult?.matches,
            };
        });
    }

    // Get search suggestions for users
    getUserSuggestions(query: string, limit: number = 5): string[] {
        if (!query || query.length < 2 || !this.usersFuse) return [];

        const results = this.usersFuse.search(query, { limit });
        const suggestions = new Set<string>();

        results.forEach(result => {
            // Add name suggestions
            if (result.item.name.toLowerCase().includes(query.toLowerCase())) {
                suggestions.add(result.item.name);
            }
            // Add college suggestions
            if (result.item.college.toLowerCase().includes(query.toLowerCase())) {
                suggestions.add(result.item.college);
            }
            // Add department suggestions
            if (result.item.department.toLowerCase().includes(query.toLowerCase())) {
                suggestions.add(result.item.department);
            }
        });

        return Array.from(suggestions).slice(0, limit);
    }

    // Get search suggestions for attendance
    getAttendanceSuggestions(query: string, limit: number = 5): string[] {
        if (!query || query.length < 2 || !this.attendanceFuse) return [];

        const results = this.attendanceFuse.search(query, { limit });
        const suggestions = new Set<string>();

        results.forEach(result => {
            if (result.item.user_name.toLowerCase().includes(query.toLowerCase())) {
                suggestions.add(result.item.user_name);
            }
            if (result.item.marked_by.toLowerCase().includes(query.toLowerCase())) {
                suggestions.add(result.item.marked_by);
            }
        });

        return Array.from(suggestions).slice(0, limit);
    }

    // Apply filters to user results
    private applyUserFilters(users: User[], filters: SearchFilters): User[] {
        return users.filter(user => {
            // Year filter
            if (filters.year && filters.year !== 'all' && user.year !== filters.year) {
                return false;
            }

            // Gender filter
            if (filters.gender && filters.gender !== 'all' && user.gender !== filters.gender) {
                return false;
            }

            // College filter
            if (filters.college && filters.college !== 'all' && !user.college.toLowerCase().includes(filters.college.toLowerCase())) {
                return false;
            }

            // Department filter
            if (filters.department && filters.department !== 'all' && !user.department.toLowerCase().includes(filters.department.toLowerCase())) {
                return false;
            }

            return true;
        });
    }

    // Apply filters to attendance results
    private applyAttendanceFilters(records: AttendanceRecord[], filters: SearchFilters): AttendanceRecord[] {
        return records.filter(record => {
            // Present/absent filter
            if (filters.present !== undefined && filters.present !== 'all' && record.present !== filters.present) {
                return false;
            }

            // Date range filter
            if (filters.dateFrom && record.date < filters.dateFrom) {
                return false;
            }
            if (filters.dateTo && record.date > filters.dateTo) {
                return false;
            }

            // Marked by filter
            if (filters.markedBy && filters.markedBy !== 'all' && !record.marked_by.toLowerCase().includes(filters.markedBy.toLowerCase())) {
                return false;
            }

            return true;
        });
    }

    // Highlight search matches in text
    highlightMatches(text: string, matches: Fuse.FuseResultMatch[] = []): string {
        if (!matches.length) return text;

        let highlightedText = text;
        const highlights: Array<{ start: number; end: number }> = [];

        matches.forEach(match => {
            if (match.indices) {
                match.indices.forEach(([start, end]) => {
                    highlights.push({ start, end });
                });
            }
        });

        // Sort highlights by start position (descending) to avoid index shifting
        highlights.sort((a, b) => b.start - a.start);

        highlights.forEach(({ start, end }) => {
            const before = highlightedText.slice(0, start);
            const highlighted = highlightedText.slice(start, end + 1);
            const after = highlightedText.slice(end + 1);
            highlightedText = `${before}<mark class="bg-yellow-200 px-1 rounded">${highlighted}</mark>${after}`;
        });

        return highlightedText;
    }

    // Get faceted search results (for filters)
    getUserFacets(query: string = '*'): {
        years: Array<{ value: number; count: number }>;
        genders: Array<{ value: string; count: number }>;
        colleges: Array<{ value: string; count: number }>;
        departments: Array<{ value: string; count: number }>;
    } {
        const results = query === '*' ? this.users : this.searchUsers(query).map(r => r.item);

        const years = this.getFacetCounts(results, 'year');
        const genders = this.getFacetCounts(results, 'gender');
        const colleges = this.getFacetCounts(results, 'college');
        const departments = this.getFacetCounts(results, 'department');

        return { years, genders, colleges, departments };
    }

    // Helper function to get facet counts
    private getFacetCounts<T>(items: T[], field: keyof T): Array<{ value: any; count: number }> {
        const counts = new Map();
        
        items.forEach(item => {
            const value = item[field];
            counts.set(value, (counts.get(value) || 0) + 1);
        });

        return Array.from(counts.entries())
            .map(([value, count]) => ({ value, count }))
            .sort((a, b) => b.count - a.count);
    }
}

// Create singleton instance
export const searchService = new SearchService();

// Export utility functions
export const initializeSearch = (users: User[], attendanceRecords: AttendanceRecord[]) => {
    searchService.initializeUsers(users);
    searchService.initializeAttendance(attendanceRecords);
};

export const searchUsers = (query: string, filters?: SearchFilters) => {
    return searchService.searchUsers(query, filters);
};

export const searchAttendance = (query: string, filters?: SearchFilters) => {
    return searchService.searchAttendance(query, filters);
};

export const getUserSuggestions = (query: string, limit?: number) => {
    return searchService.getUserSuggestions(query, limit);
};

export const getAttendanceSuggestions = (query: string, limit?: number) => {
    return searchService.getAttendanceSuggestions(query, limit);
};
