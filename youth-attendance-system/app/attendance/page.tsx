"use client"

import { useState, useEffect, use<PERSON>em<PERSON> } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import {
  UserCheck,
  UserX,
  Search,
  Calendar,
  Users,
  Download,
  CheckCircle,
  XCircle,
  BarChart3,
  ChevronUp,
  ChevronDown,
  ArrowUpDown,
} from "lucide-react"
import { useRTL } from "@/contexts/rtl-context"
import { useAppStore } from "@/stores/app-store"
import { cn } from "@/lib/utils"

interface AttendanceRecord {
  id: string
  user_id: string
  date: string
  present: boolean
  timestamp: string
}

interface User {
  id: string
  name: string
  college: string
  year: number
  phone: string
  address: string
  birth_date: string
}

type SortField = "name" | "college" | "year" | "status" | "timestamp"
type SortDirection = "asc" | "desc"

export default function AttendancePage() {
  const { isRTL, direction } = useRTL()
  const { users, attendanceRecords, addAttendanceRecord } = useAppStore()

  // Safe date initialization
  const [dateAttendance, setDateAttendance] = useState(() => {
    try {
      return new Date().toISOString().split("T")[0]
    } catch {
      return (
        new Date().getFullYear() +
        "-" +
        String(new Date().getMonth() + 1).padStart(2, "0") +
        "-" +
        String(new Date().getDate()).padStart(2, "0")
      )
    }
  })

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set())
  const [activeTab, setActiveTab] = useState("quick")
  const [sortField, setSortField] = useState<SortField>("name")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Safe date formatting functions
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return dateString
      return date.toLocaleDateString("ar-EG")
    } catch {
      return dateString
    }
  }

  const formatTime = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return dateString
      return date.toLocaleTimeString("ar-EG", { hour: "2-digit", minute: "2-digit" })
    } catch {
      return dateString
    }
  }

  const formatDateShort = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return dateString
      return date.toLocaleDateString("ar-EG", { month: "short", day: "numeric" })
    } catch {
      return dateString
    }
  }

  // Get today's attendance records
  const todayAttendance = useMemo(() => {
    return attendanceRecords.filter((record) => record.date === dateAttendance)
  }, [attendanceRecords, dateAttendance])

  // Calculate statistics
  const stats = useMemo(() => {
    const totalUsers = users.length
    const presentToday = todayAttendance.filter((record) => record.present).length
    const absentToday = totalUsers - presentToday
    const attendanceRate = totalUsers > 0 ? Math.round((presentToday / totalUsers) * 100) : 0

    return {
      totalUsers,
      presentToday,
      absentToday,
      attendanceRate,
    }
  }, [users.length, todayAttendance])

  // Filter users based on search term and year
  const getFilteredUsers = (year?: number) => {
    let filtered = users

    if (year) {
      filtered = filtered.filter((user) => user.year === year)
    }

    if (searchTerm) {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.college.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    return filtered
  }

  // Get attendance records with user info for table view
  const getAttendanceTableData = (year?: number) => {
    const filtered = getFilteredUsers(year)

    return filtered
      .map((user) => {
        const userAttendance = todayAttendance.find((record) => record.user_id === user.id)
        return {
          ...user,
          present: userAttendance?.present || false,
          timestamp: userAttendance?.timestamp || "",
          hasRecord: !!userAttendance,
        }
      })
      .sort((a, b) => {
        let aValue: any = a[sortField]
        let bValue: any = b[sortField]

        if (sortField === "status") {
          aValue = a.present ? "present" : "absent"
          bValue = b.present ? "present" : "absent"
        }

        if (sortField === "timestamp") {
          aValue = a.timestamp || "0"
          bValue = b.timestamp || "0"
        }

        if (typeof aValue === "string") {
          aValue = aValue.toLowerCase()
          bValue = bValue.toLowerCase()
        }

        if (sortDirection === "asc") {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })
  }

  // Handle attendance marking
  const markAttendance = (userId: string, present: boolean) => {
    const existingRecord = todayAttendance.find((record) => record.user_id === userId)

    if (existingRecord) {
      // Update existing record
      const updatedRecords = attendanceRecords.map((record) =>
        record.id === existingRecord.id ? { ...record, present, timestamp: new Date().toISOString() } : record,
      )
      // Note: In a real app, you'd update the store here
    } else {
      // Add new record
      addAttendanceRecord({
        id: `${userId}-${dateAttendance}`,
        user_id: userId,
        date: dateAttendance,
        present,
        timestamp: new Date().toISOString(),
      })
    }
  }

  // Handle bulk attendance
  const markBulkAttendance = (present: boolean) => {
    selectedUsers.forEach((userId) => {
      markAttendance(userId, present)
    })
    setSelectedUsers(new Set())
  }

  // Handle user selection
  const toggleUserSelection = (userId: string) => {
    const newSelected = new Set(selectedUsers)
    if (newSelected.has(userId)) {
      newSelected.delete(userId)
    } else {
      newSelected.add(userId)
    }
    setSelectedUsers(newSelected)
  }

  // Handle sort
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // Export data
  const exportData = () => {
    const data = getAttendanceTableData()
    const csv = [
      ["الاسم", "الكلية", "السنة", "الحالة", "الوقت"].join(","),
      ...data.map((row) =>
        [
          row.name,
          row.college,
          row.year,
          row.present ? "حاضر" : "غائب",
          row.timestamp ? formatTime(row.timestamp) : "",
        ].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    link.href = URL.createObjectURL(blob)
    link.download = `attendance-${dateAttendance}.csv`
    link.click()
  }

  if (!mounted) return null

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />
    return sortDirection === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
  }

  return (
    <div className="p-6 space-y-6 font-cairo" dir={direction}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className={cn("text-3xl font-bold text-gray-900", isRTL ? "text-right" : "text-left")}>تسجيل الحضور</h1>
          <p className={cn("text-gray-600 mt-1", isRTL ? "text-right" : "text-left")}>إدارة حضور الأعضاء للاجتماعات</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            تصدير
          </Button>
        </div>
      </div>

      {/* Date Selection */}
      <Card>
        <CardHeader>
          <CardTitle className={cn("flex items-center gap-2", isRTL ? "flex-row-reverse" : "flex-row")}>
            <Calendar className="h-5 w-5" />
            اختيار التاريخ
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Input
              type="date"
              value={dateAttendance}
              onChange={(e) => setDateAttendance(e.target.value)}
              className="w-auto"
            />
            <Badge variant="outline">{formatDate(dateAttendance)}</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الأعضاء</p>
                <p className="text-2xl font-bold">{stats.totalUsers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الحاضرون</p>
                <p className="text-2xl font-bold text-green-600">{stats.presentToday}</p>
              </div>
              <UserCheck className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الغائبون</p>
                <p className="text-2xl font-bold text-red-600">{stats.absentToday}</p>
              </div>
              <UserX className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">معدل الحضور</p>
                <p className="text-2xl font-bold text-blue-600">{stats.attendanceRate}%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="quick">عرض سريع</TabsTrigger>
          <TabsTrigger value="year1">السنة الأولى</TabsTrigger>
          <TabsTrigger value="year2">السنة الثانية</TabsTrigger>
          <TabsTrigger value="year3">السنة الثالثة</TabsTrigger>
          <TabsTrigger value="table">جدول السجلات</TabsTrigger>
        </TabsList>

        {/* Search */}
        <div className="mt-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="البحث بالاسم أو الكلية..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedUsers.size > 0 && (
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">تم اختيار {selectedUsers.size} عضو</span>
                <div className="flex gap-2">
                  <Button size="sm" onClick={() => markBulkAttendance(true)}>
                    <CheckCircle className="h-4 w-4 mr-1" />
                    تسجيل حضور
                  </Button>
                  <Button size="sm" variant="destructive" onClick={() => markBulkAttendance(false)}>
                    <XCircle className="h-4 w-4 mr-1" />
                    تسجيل غياب
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setSelectedUsers(new Set())}>
                    إلغاء التحديد
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick View */}
        <TabsContent value="quick" className="space-y-4">
          <div className="grid gap-4">
            {getFilteredUsers().map((user) => {
              const attendance = todayAttendance.find((record) => record.user_id === user.id)
              const isPresent = attendance?.present || false
              const isSelected = selectedUsers.has(user.id)

              return (
                <Card
                  key={user.id}
                  className={cn(
                    "transition-all duration-200",
                    isPresent ? "bg-green-50 border-green-200" : "bg-gray-50",
                    isSelected ? "ring-2 ring-blue-500" : "",
                  )}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Checkbox checked={isSelected} onCheckedChange={() => toggleUserSelection(user.id)} />
                        <div>
                          <h3 className="font-semibold">{user.name}</h3>
                          <p className="text-sm text-gray-600">
                            {user.college} - السنة {user.year}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {attendance && (
                          <Badge variant="outline" className="text-xs">
                            {formatTime(attendance.timestamp)}
                          </Badge>
                        )}
                        <Badge variant={isPresent ? "default" : "destructive"}>{isPresent ? "حاضر" : "غائب"}</Badge>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant={isPresent ? "default" : "outline"}
                            onClick={() => markAttendance(user.id, true)}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant={!isPresent && attendance ? "destructive" : "outline"}
                            onClick={() => markAttendance(user.id, false)}
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        {/* Year-specific tabs */}
        {[1, 2, 3].map((year) => (
          <TabsContent key={year} value={`year${year}`} className="space-y-4">
            <div className="grid gap-4">
              {getFilteredUsers(year).map((user) => {
                const attendance = todayAttendance.find((record) => record.user_id === user.id)
                const isPresent = attendance?.present || false
                const isSelected = selectedUsers.has(user.id)

                return (
                  <Card
                    key={user.id}
                    className={cn(
                      "transition-all duration-200",
                      isPresent ? "bg-green-50 border-green-200" : "bg-gray-50",
                      isSelected ? "ring-2 ring-blue-500" : "",
                    )}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Checkbox checked={isSelected} onCheckedChange={() => toggleUserSelection(user.id)} />
                          <div>
                            <h3 className="font-semibold">{user.name}</h3>
                            <p className="text-sm text-gray-600">{user.college}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {attendance && (
                            <Badge variant="outline" className="text-xs">
                              {formatTime(attendance.timestamp)}
                            </Badge>
                          )}
                          <Badge variant={isPresent ? "default" : "destructive"}>{isPresent ? "حاضر" : "غائب"}</Badge>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant={isPresent ? "default" : "outline"}
                              onClick={() => markAttendance(user.id, true)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant={!isPresent && attendance ? "destructive" : "outline"}
                              onClick={() => markAttendance(user.id, false)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </TabsContent>
        ))}

        {/* Table View */}
        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>سجلات الحضور</CardTitle>
              <CardDescription>عرض تفصيلي لسجلات الحضور مع إمكانية الفرز والبحث</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-2">
                        <Checkbox
                          checked={
                            selectedUsers.size === getAttendanceTableData().length &&
                            getAttendanceTableData().length > 0
                          }
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedUsers(new Set(getAttendanceTableData().map((u) => u.id)))
                            } else {
                              setSelectedUsers(new Set())
                            }
                          }}
                        />
                      </th>
                      <th className="text-right p-2 cursor-pointer hover:bg-gray-50" onClick={() => handleSort("name")}>
                        <div className="flex items-center gap-1">
                          الاسم
                          <SortIcon field="name" />
                        </div>
                      </th>
                      <th
                        className="text-right p-2 cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("college")}
                      >
                        <div className="flex items-center gap-1">
                          الكلية
                          <SortIcon field="college" />
                        </div>
                      </th>
                      <th className="text-right p-2 cursor-pointer hover:bg-gray-50" onClick={() => handleSort("year")}>
                        <div className="flex items-center gap-1">
                          السنة
                          <SortIcon field="year" />
                        </div>
                      </th>
                      <th
                        className="text-right p-2 cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("status")}
                      >
                        <div className="flex items-center gap-1">
                          الحالة
                          <SortIcon field="status" />
                        </div>
                      </th>
                      <th
                        className="text-right p-2 cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort("timestamp")}
                      >
                        <div className="flex items-center gap-1">
                          الوقت
                          <SortIcon field="timestamp" />
                        </div>
                      </th>
                      <th className="text-right p-2">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {getAttendanceTableData().map((user) => (
                      <tr
                        key={user.id}
                        className={cn(
                          "border-b hover:bg-gray-50 transition-colors",
                          user.present ? "bg-green-50/50" : "bg-red-50/50",
                        )}
                      >
                        <td className="p-2">
                          <Checkbox
                            checked={selectedUsers.has(user.id)}
                            onCheckedChange={() => toggleUserSelection(user.id)}
                          />
                        </td>
                        <td className="p-2 font-medium">{user.name}</td>
                        <td className="p-2 text-gray-600">{user.college}</td>
                        <td className="p-2">
                          <Badge variant="outline">السنة {user.year}</Badge>
                        </td>
                        <td className="p-2">
                          <Badge variant={user.present ? "default" : "destructive"}>
                            {user.present ? "حاضر" : "غائب"}
                          </Badge>
                        </td>
                        <td className="p-2 text-sm text-gray-500">
                          {user.timestamp ? formatTime(user.timestamp) : "-"}
                        </td>
                        <td className="p-2">
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant={user.present ? "default" : "outline"}
                              onClick={() => markAttendance(user.id, true)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant={!user.present && user.hasRecord ? "destructive" : "outline"}
                              onClick={() => markAttendance(user.id, false)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
