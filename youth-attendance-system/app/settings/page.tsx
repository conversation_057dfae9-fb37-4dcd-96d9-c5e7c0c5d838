"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { SettingsIcon, Save, Bell, Clock, MessageSquare, Database, Globe, CheckCircle } from "lucide-react"
import { useAppStore } from "@/stores/app-store"
import { useRTL } from "@/contexts/rtl-context"
import { cn } from "@/lib/utils"

const settingsSchema = z.object({
  auto_mark_absent: z.boolean(),
  absent_cutoff_time: z.string(),
  birthday_notifications: z.boolean(),
  notification_frequency: z.enum(["weekly", "monthly", "end_of_month"]),
  whatsapp_message_template: z.string().min(10, "نص الرسالة يجب أن يكون أكثر من 10 أحرف"),
})

type SettingsData = z.infer<typeof settingsSchema>

export default function SettingsPage() {
  const { settings, updateSettings } = useAppStore()
  const { isRTL, direction, toggleDirection } = useRTL()
  const [showSuccess, setShowSuccess] = useState(false)

  const form = useForm<SettingsData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: settings,
  })

  const onSubmit = (data: SettingsData) => {
    updateSettings(data)
    setShowSuccess(true)
    setTimeout(() => setShowSuccess(false), 3000)
  }

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div className={cn("flex items-center gap-3 mb-2", isRTL ? "flex-row-reverse" : "flex-row")}>
          <div className="p-2 bg-gradient-to-r from-gray-500 to-slate-500 rounded-lg">
            <SettingsIcon className="h-6 w-6 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-heading gradient-text">الإعدادات</h1>
        </div>
        <p className={cn("text-gray-600 text-lg text-body", isRTL ? "text-right" : "text-left")}>
          إعدادات النظام والتحكم في الميزات المختلفة
        </p>
      </div>

      {/* Success Message */}
      {showSuccess && (
        <Card className="animate-scale-in border-green-200 bg-green-50">
          <CardContent className="flex items-center gap-3 p-4">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="text-green-800 font-medium">تم حفظ الإعدادات بنجاح!</span>
          </CardContent>
        </Card>
      )}

      <div className="max-w-4xl mx-auto space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* General Settings */}
            <Card className="animate-scale-in">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5" />
                  الإعدادات العامة
                </CardTitle>
                <CardDescription>الإعدادات الأساسية للنظام</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base font-medium">اتجاه الواجهة</Label>
                    <p className="text-sm text-gray-500">تغيير اتجاه النص والواجهة بين العربية والإنجليزية</p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={toggleDirection}
                    className="flex items-center gap-2 bg-transparent"
                  >
                    <Globe className="h-4 w-4" />
                    {isRTL ? "English" : "العربية"}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Attendance Settings */}
            <Card className="animate-scale-in" style={{ animationDelay: "0.1s" }}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  إعدادات الحضور
                </CardTitle>
                <CardDescription>التحكم في آلية تسجيل الحضور والغياب</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="auto_mark_absent"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">تسجيل الغياب التلقائي</FormLabel>
                        <FormDescription>تسجيل الأعضاء كغائبين تلقائياً بعد وقت محدد</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="absent_cutoff_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>وقت قطع تسجيل الحضور</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormDescription>الوقت الذي يتم بعده تسجيل الأعضاء كغائبين تلقائياً</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Birthday Notifications */}
            <Card className="animate-scale-in" style={{ animationDelay: "0.2s" }}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  إشعارات أعياد الميلاد
                </CardTitle>
                <CardDescription>إعدادات التذكير بأعياد ميلاد الأعضاء</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="birthday_notifications"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">تفعيل إشعارات أعياد الميلاد</FormLabel>
                        <FormDescription>إرسال تذكيرات بأعياد ميلاد الأعضاء</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notification_frequency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>تكرار الإشعارات</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر تكرار الإشعارات" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="weekly">أسبوعياً</SelectItem>
                          <SelectItem value="monthly">شهرياً (أول الشهر)</SelectItem>
                          <SelectItem value="end_of_month">شهرياً (آخر الشهر)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>كم مرة تريد الحصول على تذكيرات أعياد الميلاد</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* WhatsApp Settings */}
            <Card className="animate-scale-in" style={{ animationDelay: "0.3s" }}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  إعدادات رسائل الواتساب
                </CardTitle>
                <CardDescription>تخصيص رسائل التهنئة بأعياد الميلاد</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="whatsapp_message_template"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>نموذج رسالة عيد الميلاد</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="كل سنة وانت طيب يا {{name}}! 🎉"
                          className="resize-none"
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>استخدم {`{{name}}`} لإدراج اسم العضو في الرسالة</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">معاينة الرسالة:</h4>
                  <p className="text-blue-800">
                    {form.watch("whatsapp_message_template")?.replace("{{name}}", "أحمد محمد") ||
                      "كل سنة وانت طيب يا أحمد محمد! 🎉 ربنا يفرح قلبك دايماً"}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Data Management */}
            <Card className="animate-scale-in" style={{ animationDelay: "0.4s" }}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  إدارة البيانات
                </CardTitle>
                <CardDescription>نسخ احتياطي وإدارة بيانات النظام</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button variant="outline" className="h-12 bg-transparent">
                    <Database className="h-4 w-4 mr-2" />
                    تصدير البيانات
                  </Button>
                  <Button variant="outline" className="h-12 bg-transparent">
                    <Database className="h-4 w-4 mr-2" />
                    استيراد البيانات
                  </Button>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>تنبيه:</strong> تأكد من عمل نسخة احتياطية من البيانات بانتظام لتجنب فقدانها.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Save Button */}
            <div className="flex justify-end pt-6">
              <Button type="submit" className="btn-gradient px-8 py-3 text-lg" disabled={form.formState.isSubmitting}>
                <Save className="h-5 w-5 mr-2" />
                {form.formState.isSubmitting ? "جاري الحفظ..." : "حفظ الإعدادات"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}
