import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Cairo } from "next/font/google"
import "./globals.css"
import { AuthProvider } from "@/contexts/auth-context"
import { RTLProvider } from "@/contexts/rtl-context"
import { Toaster } from "@/components/ui/toaster"
import { AppInitializer } from "@/components/app-initializer"
import { GlobalLayout } from "@/components/layout/global-layout"

// Configure Cairo font with Next.js font optimization
const cairo = Cairo({
  subsets: ["arabic", "latin"],
  weight: ["200", "300", "400", "500", "600", "700", "800", "900"],
  style: ["normal"],
  display: "swap",
  variable: "--font-cairo",
  preload: true,
})

export const metadata: Metadata = {
  title: "نظام حضور الشباب",
  description: "نظام إدارة حضور الشباب في الكنيسة",
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#3b82f6",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html className={cairo.variable}>
      <body className={`${cairo.className} antialiased`}>
        <RTLProvider>
          <AuthProvider>
            <AppInitializer />
            <GlobalLayout>{children}</GlobalLayout>
            <Toaster />
          </AuthProvider>
        </RTLProvider>
      </body>
    </html>
  )
}
