"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import {
  UserPlus,
  Save,
  QrCode,
  Download,
  Share,
  CheckCircle,
  User,
  Phone,
  Calendar,
  MapPin,
  GraduationCap,
  Facebook,
} from "lucide-react"
import { useAppStore } from "@/stores/app-store"
import { useRTL } from "@/contexts/rtl-context"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

const formSchema = z.object({
  name: z.string().min(2, "الاسم يجب أن يكون أكثر من حرفين"),
  phone: z.string().min(11, "رقم الهاتف يجب أن يكون 11 رقم على الأقل"),
  gender: z.enum(["male", "female"], { required_error: "يرجى اختيار النوع" }),
  year: z.enum(["1", "2", "3", "4"], { required_error: "يرجى اختيار السنة الدراسية" }),
  college: z.string().min(2, "اسم الكلية مطلوب"),
  department: z.string().min(2, "اسم القسم مطلوب"),
  birthdate: z.string().min(1, "تاريخ الميلاد مطلوب"),
  address: z.string().min(5, "العنوان يجب أن يكون أكثر تفصيلاً"),
  facebook_url: z.string().optional(),
  first_attendance_date: z.string().min(1, "تاريخ أول حضور مطلوب"),
})

type FormData = z.infer<typeof formSchema>

export default function AddUserPage() {
  const { addUser } = useAppStore()
  const { isRTL, direction } = useRTL()
  const [showSuccess, setShowSuccess] = useState(false)
  const [generatedQR, setGeneratedQR] = useState<string | null>(null)

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      phone: "",
      gender: undefined,
      year: undefined,
      college: "",
      department: "",
      birthdate: "",
      address: "",
      facebook_url: "",
      first_attendance_date: format(new Date(), "yyyy-MM-dd"),
    },
  })

  const onSubmit = (data: FormData) => {
    const userData = {
      ...data,
      year: Number.parseInt(data.year) as 1 | 2 | 3 | 4,
    }

    addUser(userData)
    setGeneratedQR(`QR_${Date.now()}`)
    setShowSuccess(true)

    // Reset form after 3 seconds
    setTimeout(() => {
      setShowSuccess(false)
      setGeneratedQR(null)
      form.reset()
    }, 5000)
  }

  if (showSuccess) {
    return (
      <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
        <div className="max-w-2xl mx-auto">
          <Card className="animate-scale-in border-green-200 bg-green-50">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
              </div>
              <CardTitle className="text-2xl text-green-800">تم إضافة العضو بنجاح!</CardTitle>
              <CardDescription className="text-green-600">تم إنشاء حساب العضو وتوليد QR Code الخاص به</CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="w-48 h-48 bg-white rounded-lg mx-auto flex items-center justify-center border-2 border-green-200">
                <QrCode className="h-24 w-24 text-green-500" />
              </div>
              <div>
                <h3 className="font-medium text-green-800 mb-2">QR Code جاهز للاستخدام</h3>
                <p className="text-sm text-green-600">يمكن للعضو استخدام هذا الرمز لتسجيل الحضور</p>
              </div>
              <div className="flex gap-3 justify-center">
                <Button variant="outline" className="border-green-300 text-green-700 bg-transparent">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل QR
                </Button>
                <Button variant="outline" className="border-green-300 text-green-700 bg-transparent">
                  <Share className="h-4 w-4 mr-2" />
                  مشاركة
                </Button>
                <Button
                  className="bg-green-600 hover:bg-green-700"
                  onClick={() => {
                    setShowSuccess(false)
                    setGeneratedQR(null)
                    form.reset()
                  }}
                >
                  إضافة عضو آخر
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div className={cn("flex items-center gap-3 mb-2", isRTL ? "flex-row-reverse" : "flex-row")}>
          <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
            <UserPlus className="h-6 w-6 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-heading gradient-text">إضافة عضو جديد</h1>
        </div>
        <p className={cn("text-gray-600 text-lg text-body", isRTL ? "text-right" : "text-left")}>
          إضافة عضو جديد لنظام حضور الشباب مع توليد QR Code
        </p>
      </div>

      <div className="max-w-4xl mx-auto">
        <Card className="animate-scale-in">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              بيانات العضو الجديد
            </CardTitle>
            <CardDescription>يرجى ملء جميع البيانات المطلوبة لإضافة العضو الجديد</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Personal Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">البيانات الشخصية</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            الاسم الكامل
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="أدخل الاسم الكامل" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            رقم الهاتف
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="01xxxxxxxxx" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>النوع</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر النوع" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="male">ذكر</SelectItem>
                              <SelectItem value="female">أنثى</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="birthdate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            تاريخ الميلاد
                          </FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Academic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">البيانات الأكاديمية</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="year"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>السنة الدراسية</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر السنة" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="1">السنة الأولى</SelectItem>
                              <SelectItem value="2">السنة الثانية</SelectItem>
                              <SelectItem value="3">السنة الثالثة</SelectItem>
                              <SelectItem value="4">السنة الرابعة</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="college"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <GraduationCap className="h-4 w-4" />
                            الكلية
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="اسم الكلية" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="department"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>القسم</FormLabel>
                          <FormControl>
                            <Input placeholder="اسم القسم" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">معلومات الاتصال</h3>

                  <div className="grid grid-cols-1 gap-4">
                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            العنوان
                          </FormLabel>
                          <FormControl>
                            <Textarea placeholder="العنوان التفصيلي" className="resize-none" rows={3} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="facebook_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Facebook className="h-4 w-4" />
                            رابط الفيسبوك (اختياري)
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="https://facebook.com/username" {...field} />
                          </FormControl>
                          <FormDescription>رابط الصفحة الشخصية على الفيسبوك (اختياري)</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="first_attendance_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            تاريخ أول حضور
                          </FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormDescription>تاريخ أول مرة حضر فيها العضو (افتراضياً اليوم)</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-6 border-t">
                  <Button
                    type="submit"
                    className="btn-gradient px-8 py-3 text-lg"
                    disabled={form.formState.isSubmitting}
                  >
                    <Save className="h-5 w-5 mr-2" />
                    {form.formState.isSubmitting ? "جاري الحفظ..." : "حفظ وإنشاء QR Code"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
