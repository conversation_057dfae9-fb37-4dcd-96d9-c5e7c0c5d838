import { create } from "zustand"
import { persist } from "zustand/middleware"
import { initializeMockData } from "@/lib/mock-data"

export interface User {
  id: string
  name: string
  phone: string
  gender: "male" | "female"
  year: 1 | 2 | 3 | 4
  college: string
  department: string
  birthdate: string
  address: string
  facebook_url?: string
  first_attendance_date: string
  qr_code?: string
  created_at: string
  updated_at: string
}

export interface AttendanceRecord {
  id: string
  user_id: string
  user_name: string
  date: string
  present: boolean
  marked_by: string
  created_at: string
}

export interface AppSettings {
  auto_mark_absent: boolean
  absent_cutoff_time: string
  birthday_notifications: boolean
  notification_frequency: "weekly" | "monthly" | "end_of_month"
  whatsapp_message_template: string
}

interface AppState {
  // Users
  users: User[]
  selectedUser: User | null

  // Attendance
  attendanceRecords: AttendanceRecord[]
  todayAttendance: AttendanceRecord[]

  // Settings
  settings: AppSettings

  // UI State
  loading: boolean
  error: string | null
  initialized: boolean

  // Actions
  initializeWithMockData: () => void
  setUsers: (users: User[]) => void
  addUser: (user: Omit<User, "id" | "created_at" | "updated_at">) => void
  updateUser: (id: string, user: Partial<User>) => void
  deleteUser: (id: string) => void
  setSelectedUser: (user: User | null) => void

  setAttendanceRecords: (records: AttendanceRecord[]) => void
  markAttendance: (userId: string, present: boolean, markedBy: string) => void
  bulkMarkAttendance: (userIds: string[], present: boolean, markedBy: string) => void

  updateSettings: (settings: Partial<AppSettings>) => void

  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Export functions
  exportUsers: (format: "csv" | "excel" | "pdf", filters?: any) => void
  exportAttendance: (format: "csv" | "excel" | "pdf", filters?: any) => void
  exportReports: (format: "csv" | "excel" | "pdf", reportType: string, filters?: any) => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      users: [],
      selectedUser: null,
      attendanceRecords: [],
      todayAttendance: [],
      settings: {
        auto_mark_absent: true,
        absent_cutoff_time: "21:00",
        birthday_notifications: true,
        notification_frequency: "weekly",
        whatsapp_message_template: "كل سنة وانت طيب يا {{name}}! 🎉 ربنا يفرح قلبك دايماً",
      },
      loading: false,
      error: null,
      initialized: false,

      // Actions
      initializeWithMockData: () => {
        const { users, attendanceRecords } = initializeMockData()
        set({
          users,
          attendanceRecords,
          initialized: true,
        })
      },

      setUsers: (users) => set({ users }),

      addUser: (userData) => {
        const newUser: User = {
          ...userData,
          id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          qr_code: `QR_${Date.now()}`,
        }
        set((state) => ({ users: [...state.users, newUser] }))
      },

      updateUser: (id, userData) => {
        set((state) => ({
          users: state.users.map((user) =>
            user.id === id ? { ...user, ...userData, updated_at: new Date().toISOString() } : user,
          ),
        }))
      },

      deleteUser: (id) => {
        set((state) => ({
          users: state.users.filter((user) => user.id !== id),
          selectedUser: state.selectedUser?.id === id ? null : state.selectedUser,
        }))
      },

      setSelectedUser: (user) => set({ selectedUser: user }),

      setAttendanceRecords: (records) => set({ attendanceRecords: records }),

      markAttendance: (userId, present, markedBy) => {
        const today = new Date().toISOString().split("T")[0]
        const user = get().users.find((u) => u.id === userId)

        if (!user) return

        const newRecord: AttendanceRecord = {
          id: `attendance_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          user_id: userId,
          user_name: user.name,
          date: today,
          present,
          marked_by: markedBy,
          created_at: new Date().toISOString(),
        }

        set((state) => ({
          attendanceRecords: [
            ...state.attendanceRecords.filter((r) => !(r.user_id === userId && r.date === today)),
            newRecord,
          ],
          todayAttendance: [...state.todayAttendance.filter((r) => r.user_id !== userId), newRecord],
        }))
      },

      bulkMarkAttendance: (userIds, present, markedBy) => {
        const today = new Date().toISOString().split("T")[0]
        const users = get().users.filter((u) => userIds.includes(u.id))

        const newRecords = users.map((user) => ({
          id: `attendance_${Date.now()}_${user.id}_${Math.random().toString(36).substr(2, 9)}`,
          user_id: user.id,
          user_name: user.name,
          date: today,
          present,
          marked_by: markedBy,
          created_at: new Date().toISOString(),
        }))

        set((state) => ({
          attendanceRecords: [
            ...state.attendanceRecords.filter((r) => !(userIds.includes(r.user_id) && r.date === today)),
            ...newRecords,
          ],
          todayAttendance: [...state.todayAttendance.filter((r) => !userIds.includes(r.user_id)), ...newRecords],
        }))
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        }))
      },

      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      // Export functions
      exportUsers: (format, filters) => {
        const { users } = get()
        let filteredUsers = users

        if (filters) {
          if (filters.year) filteredUsers = filteredUsers.filter((u) => u.year === filters.year)
          if (filters.college) filteredUsers = filteredUsers.filter((u) => u.college.includes(filters.college))
          if (filters.gender) filteredUsers = filteredUsers.filter((u) => u.gender === filters.gender)
        }

        if (format === "csv") {
          const csvContent = [
            ["الاسم", "الهاتف", "النوع", "السنة", "الكلية", "القسم", "تاريخ الميلاد", "العنوان"].join(","),
            ...filteredUsers.map((user) =>
              [
                user.name,
                user.phone,
                user.gender === "male" ? "ذكر" : "أنثى",
                user.year,
                user.college,
                user.department,
                user.birthdate,
                user.address,
              ].join(","),
            ),
          ].join("\n")

          const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
          const link = document.createElement("a")
          link.href = URL.createObjectURL(blob)
          link.download = `users_${format(new Date(), "yyyy-MM-dd")}.csv`
          link.click()
        }
      },

      exportAttendance: (format, filters) => {
        const { attendanceRecords, users } = get()
        let filteredRecords = attendanceRecords

        if (filters) {
          if (filters.dateFrom) filteredRecords = filteredRecords.filter((r) => r.date >= filters.dateFrom)
          if (filters.dateTo) filteredRecords = filteredRecords.filter((r) => r.date <= filters.dateTo)
          if (filters.userId) filteredRecords = filteredRecords.filter((r) => r.user_id === filters.userId)
        }

        if (format === "csv") {
          const csvContent = [
            ["الاسم", "التاريخ", "الحالة", "المسجل بواسطة"].join(","),
            ...filteredRecords.map((record) =>
              [record.user_name, record.date, record.present ? "حاضر" : "غائب", record.marked_by].join(","),
            ),
          ].join("\n")

          const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
          const link = document.createElement("a")
          link.href = URL.createObjectURL(blob)
          link.download = `attendance_${format(new Date(), "yyyy-MM-dd")}.csv`
          link.click()
        }
      },

      exportReports: (format, reportType, filters) => {
        // Implementation for different report types
        console.log(`Exporting ${reportType} report as ${format}`, filters)
      },
    }),
    {
      name: "youth-attendance-store",
      partialize: (state) => ({
        users: state.users,
        attendanceRecords: state.attendanceRecords,
        settings: state.settings,
        initialized: state.initialized,
      }),
    },
  ),
)
