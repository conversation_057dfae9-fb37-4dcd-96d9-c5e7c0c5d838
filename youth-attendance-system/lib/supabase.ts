import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          name: string
          phone: string
          gender: "male" | "female"
          year: 1 | 2 | 3 | 4
          college: string
          department: string
          birthdate: string
          address: string
          facebook_url?: string
          first_attendance_date: string
          qr_code?: string
          created_at: string
        }
        Insert: Omit<Database["public"]["Tables"]["users"]["Row"], "id" | "created_at">
        Update: Partial<Database["public"]["Tables"]["users"]["Insert"]>
      }
      attendance: {
        Row: {
          id: string
          user_id: string
          date: string
          present: boolean
          marked_by: string
          created_at: string
        }
        Insert: Omit<Database["public"]["Tables"]["attendance"]["Row"], "id" | "created_at">
        Update: Partial<Database["public"]["Tables"]["attendance"]["Insert"]>
      }
    }
  }
}
