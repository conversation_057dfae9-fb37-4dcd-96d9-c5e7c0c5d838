"use client"

import type React from "react"

import { useEffect, useState } from "react"

export function FontLoader({ children }: { children: React.ReactNode }) {
  const [fontLoaded, setFontLoaded] = useState(false)

  useEffect(() => {
    // Check if font is loaded
    const checkFont = () => {
      if (document.fonts && document.fonts.ready) {
        document.fonts.ready.then(() => {
          setFontLoaded(true)
        })
      } else {
        // Fallback for browsers that don't support document.fonts
        setTimeout(() => setFontLoaded(true), 100)
      }
    }

    checkFont()
  }, [])

  return <div className={`transition-opacity duration-300 ${fontLoaded ? "opacity-100" : "opacity-0"}`}>{children}</div>
}
