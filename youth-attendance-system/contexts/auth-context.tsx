"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import type { User } from "@supabase/supabase-js"
import { supabase } from "@/lib/supabase"

interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    // Demo authentication for testing
    if (email === "<EMAIL>" && password === "password") {
      // Create a mock user for demo purposes
      const mockUser = {
        id: "demo-user-id",
        email: "<EMAIL>",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        email_confirmed_at: new Date().toISOString(),
        last_sign_in_at: new Date().toISOString(),
        app_metadata: {},
        user_metadata: {},
        aud: "authenticated",
        role: "authenticated",
      } as User

      setUser(mockUser)

      // Store in localStorage for persistence
      localStorage.setItem("demo-auth", JSON.stringify(mockUser))

      return { error: null }
    }

    // Try actual Supabase authentication
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signOut = async () => {
    // Clear demo auth
    localStorage.removeItem("demo-auth")
    setUser(null)

    // Also sign out from Supabase if authenticated there
    await supabase.auth.signOut()
  }

  // Check for demo auth on mount
  useEffect(() => {
    const demoAuth = localStorage.getItem("demo-auth")
    if (demoAuth && !user) {
      try {
        const mockUser = JSON.parse(demoAuth)
        setUser(mockUser)
      } catch (error) {
        localStorage.removeItem("demo-auth")
      }
    }
    setLoading(false)
  }, [user])

  return <AuthContext.Provider value={{ user, loading, signIn, signOut }}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
